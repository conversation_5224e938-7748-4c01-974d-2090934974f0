Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to crc.o(i.MX_CRC_Init) for MX_CRC_Init
    main.o(i.main) refers to speed.o(i.speed_meter_init) for speed_meter_init
    main.o(i.main) refers to nrf24l01.o(i.NRF24L01_Init) for NRF24L01_Init
    main.o(i.main) refers to nrf24l01.o(i.NRF24L01_RX_Mode) for NRF24L01_RX_Mode
    main.o(i.main) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to cmsis_os2.o(i.osKernelInitialize) for osKernelInitialize
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelStart) for osKernelStart
    main.o(i.main) refers to tim.o(.bss) for htim3
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    freertos.o(i.HAL_UART_RxCpltCallback) refers to transmit.o(i.receieve_from_board) for receieve_from_board
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osThreadNew) for osThreadNew
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartDefaultTask) for StartDefaultTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.Servo_uart_ctrl_task) for Servo_uart_ctrl_task
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.connect_Aux_task) for connect_Aux_task
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.motor_ctrl_task) for motor_ctrl_task
    freertos.o(i.Servo_uart_ctrl_task) refers to nrf24l01.o(i.NRF24L01_RxPacket) for NRF24L01_RxPacket
    freertos.o(i.Servo_uart_ctrl_task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.Servo_uart_ctrl_task) refers to freertos.o(.bss) for .bss
    freertos.o(i.Servo_uart_ctrl_task) refers to motor_ctrl.o(.bss) for remote
    freertos.o(i.StartDefaultTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.connect_Aux_task) refers to transmit.o(i.receieve_from_board) for receieve_from_board
    freertos.o(i.connect_Aux_task) refers to transmit.o(i.connect_with_board) for connect_with_board
    freertos.o(i.connect_Aux_task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.connect_Aux_task) refers to transmit.o(.bss) for main_board
    freertos.o(i.motor_ctrl_task) refers to motor_ctrl.o(i.PID_Init) for PID_Init
    freertos.o(i.motor_ctrl_task) refers to speed.o(i.get_speed) for get_speed
    freertos.o(i.motor_ctrl_task) refers to motor_ctrl.o(i.remote_recieve) for remote_recieve
    freertos.o(i.motor_ctrl_task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.motor_ctrl_task) refers to motor_ctrl.o(i.motor_ctrl_at_TB6612) for motor_ctrl_at_TB6612
    freertos.o(i.motor_ctrl_task) refers to speed.o(.data) for motor_speed
    freertos.o(i.motor_ctrl_task) refers to motor_ctrl.o(.bss) for remote
    freertos.o(i.motor_ctrl_task) refers to tim.o(.bss) for htim2
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.MX_ADC1_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    crc.o(i.MX_CRC_Init) refers to stm32f1xx_hal_crc.o(i.HAL_CRC_Init) for HAL_CRC_Init
    crc.o(i.MX_CRC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    crc.o(i.MX_CRC_Init) refers to crc.o(.data) for .data
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C2_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM3_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART2_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim4
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(i.ADC_DMAError) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f1xx_hal_adc.o(i.ADC_Enable) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.ADC_Enable) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f1xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_crc.o(i.HAL_CRC_DeInit) refers to crc.o(i.HAL_CRC_MspDeInit) for HAL_CRC_MspDeInit
    stm32f1xx_hal_crc.o(i.HAL_CRC_Init) refers to crc.o(i.HAL_CRC_MspInit) for HAL_CRC_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to freertos.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to freertos.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    motor_ctrl.o(i.PID_Cal) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    motor_ctrl.o(i.PID_Cal) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    motor_ctrl.o(i.PID_Cal) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    motor_ctrl.o(i.PID_Cal) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    motor_ctrl.o(i.PID_Cal) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_ctrl.o(i.PID_Cal) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_ctrl.o(i.PID_Cal) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    motor_ctrl.o(i.PID_Cal) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_ctrl.o(i.PID_Cal) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    motor_ctrl.o(i.PID_Cal) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_ctrl.o(i.PID_Cal) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    motor_ctrl.o(i.PID_Cal) refers to motor_ctrl.o(.data) for .data
    motor_ctrl.o(i.PID_Init) refers to motor_ctrl.o(.bss) for .bss
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to motor_ctrl.o(i.PID_Cal) for PID_Cal
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to transmit.o(.data) for OK
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to motor_ctrl.o(.data) for .data
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to motor_ctrl.o(.bss) for .bss
    motor_ctrl.o(i.motor_ctrl_at_TB6612) refers to speed.o(.data) for motor_speed
    motor_ctrl.o(i.remote_recieve) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    motor_ctrl.o(i.remote_recieve) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    motor_ctrl.o(i.remote_recieve) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_ctrl.o(i.remote_recieve) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    motor_ctrl.o(i.remote_recieve) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    motor_ctrl.o(i.remote_recieve) refers to transmit.o(.bss) for main_board
    motor_ctrl.o(i.remote_recieve) refers to motor_ctrl.o(.bss) for .bss
    motor_ctrl.o(i.remote_recieve) refers to motor_ctrl.o(.data) for .data
    speed.o(i.speed_meter_init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    speed.o(i.speed_meter_init) refers to tim.o(.bss) for htim1
    transmit.o(i.connect_with_board) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    transmit.o(i.connect_with_board) refers to transmit.o(i.CheckSum) for CheckSum
    transmit.o(i.connect_with_board) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    transmit.o(i.connect_with_board) refers to transmit.o(.bss) for .bss
    transmit.o(i.connect_with_board) refers to usart.o(.bss) for huart2
    transmit.o(i.receieve_from_board) refers to transmit.o(i.CheckSum) for CheckSum
    transmit.o(i.receieve_from_board) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    transmit.o(i.receieve_from_board) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    transmit.o(i.receieve_from_board) refers to transmit.o(.bss) for .bss
    transmit.o(i.receieve_from_board) refers to freertos.o(.data) for CRC_test
    transmit.o(i.receieve_from_board) refers to transmit.o(.data) for .data
    transmit.o(i.receieve_from_board) refers to usart.o(.bss) for huart2
    control.o(i.AngleCalculate) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    control.o(i.AngleCalculate) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    control.o(i.AngleCalculate) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    control.o(i.AngleCalculate) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    control.o(i.AngleCalculate) refers to atan2.o(i.atan2) for atan2
    control.o(i.AngleCalculate) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    control.o(i.AngleCalculate) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    control.o(i.AngleCalculate) refers to filter.o(i.ComplementaryFilter) for ComplementaryFilter
    control.o(i.AngleCalculate) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    control.o(i.AngleCalculate) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    control.o(i.AngleCalculate) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    control.o(i.AngleCalculate) refers to control.o(.data) for .data
    control.o(i.GetMpuData) refers to mpu6050.o(i.MPU_Get_Accelerometer) for MPU_Get_Accelerometer
    control.o(i.GetMpuData) refers to mpu6050.o(i.MPU_Get_Gyroscope) for MPU_Get_Gyroscope
    control.o(i.GetMpuData) refers to control.o(.data) for .data
    filter.o(i.ComplementaryFilter) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    filter.o(i.ComplementaryFilter) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    filter.o(i.ComplementaryFilter) refers to filter.o(.data) for .data
    mpu6050.o(i.MPU_Get_Accelerometer) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    mpu6050.o(i.MPU_Get_Temperature) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    mpu6050.o(i.MPU_Get_Temperature) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    mpu6050.o(i.MPU_Get_Temperature) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    mpu6050.o(i.MPU_Get_Temperature) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mpu6050.o(i.MPU_Get_Temperature) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    mpu6050.o(i.MPU_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Gyro_Fsr) for MPU_Set_Gyro_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Accel_Fsr) for MPU_Set_Accel_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Rate) for MPU_Set_Rate
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Read_Byte) for MPU_Read_Byte
    mpu6050.o(i.MPU_Init) refers to i2c.o(.bss) for hi2c2
    mpu6050.o(i.MPU_Read_Byte) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(i.MPU_Read_Byte) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050.o(i.MPU_Read_Byte) refers to i2c.o(.bss) for hi2c2
    mpu6050.o(i.MPU_Read_Len) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(i.MPU_Read_Len) refers to i2c.o(.bss) for hi2c2
    mpu6050.o(i.MPU_Set_Accel_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Gyro_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_LPF) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Set_LPF) for MPU_Set_LPF
    mpu6050.o(i.MPU_Write_Byte) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    mpu6050.o(i.MPU_Write_Byte) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050.o(i.MPU_Write_Byte) refers to i2c.o(.bss) for hi2c2
    mpu6050.o(i.MPU_Write_Len) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    mpu6050.o(i.MPU_Write_Len) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050.o(i.MPU_Write_Len) refers to i2c.o(.bss) for hi2c2
    nrf24l01.o(i.NRF24L01_Check) refers to nrf24l01.o(i.SPI1_SetSpeed) for SPI1_SetSpeed
    nrf24l01.o(i.NRF24L01_Check) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.NRF24L01_Check) refers to nrf24l01.o(i.NRF24L01_Read_Buf) for NRF24L01_Read_Buf
    nrf24l01.o(i.NRF24L01_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    nrf24l01.o(i.NRF24L01_Init) refers to nrf24l01.o(i.NRF24L01_SPI_Init) for NRF24L01_SPI_Init
    nrf24l01.o(i.NRF24L01_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    nrf24l01.o(i.NRF24L01_Init) refers to nrf24l01.o(i.NRF24L01_Check) for NRF24L01_Check
    nrf24l01.o(i.NRF24L01_Init) refers to nrf24l01.o(.data) for .data
    nrf24l01.o(i.NRF24L01_Init) refers to main.o(.data) for NRF_OK
    nrf24l01.o(i.NRF24L01_RX_Mode) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_RX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.NRF24L01_RX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.NRF24L01_RX_Mode) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    nrf24l01.o(i.NRF24L01_RX_Mode) refers to nrf24l01.o(.constdata) for .constdata
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to nrf24l01.o(i.SPIx_ReadWriteByte) for SPIx_ReadWriteByte
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF24L01_Read_Reg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Read_Reg) refers to nrf24l01.o(i.SPIx_ReadWriteByte) for SPIx_ReadWriteByte
    nrf24l01.o(i.NRF24L01_Read_Reg) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.SPI1_SetSpeed) for SPI1_SetSpeed
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.NRF24L01_Read_Reg) for NRF24L01_Read_Reg
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.NRF24L01_Read_Buf) for NRF24L01_Read_Buf
    nrf24l01.o(i.NRF24L01_SPI_Init) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    nrf24l01.o(i.NRF24L01_SPI_Init) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF24L01_TX_Mode) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_TX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.NRF24L01_TX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.NRF24L01_TX_Mode) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    nrf24l01.o(i.NRF24L01_TX_Mode) refers to nrf24l01.o(.constdata) for .constdata
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.SPI1_SetSpeed) for SPI1_SetSpeed
    nrf24l01.o(i.NRF24L01_TxPacket) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.NRF24L01_TxPacket) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.NRF24L01_Read_Reg) for NRF24L01_Read_Reg
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to nrf24l01.o(i.SPIx_ReadWriteByte) for SPIx_ReadWriteByte
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF24L01_Write_Reg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Write_Reg) refers to nrf24l01.o(i.SPIx_ReadWriteByte) for SPIx_ReadWriteByte
    nrf24l01.o(i.NRF24L01_Write_Reg) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF_LowPower_Mode) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF_LowPower_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.SPI1_SetSpeed) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.SPIx_ReadWriteByte) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to event_groups.o(i.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to event_groups.o(i.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueGiveMutexRecursive) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(i.xQueueTakeMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueTakeMutexRecursive) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memset.o(.text) for memset
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvInitialiseNewTask) refers to aeabi_memset.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(i.vTaskGetInfo) for vTaskGetInfo
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to cmsis_os2.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to cmsis_os2.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for .data
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for .data
    cmsis_os2.o(i.TimerCallback) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os2.o(i.osDelay) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.vTaskDelayUntil) for vTaskDelayUntil
    cmsis_os2.o(i.osDelayUntil) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBitsFromISR) for xEventGroupClearBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsClear) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osEventFlagsDelete) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    cmsis_os2.o(i.osEventFlagsDelete) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsGet) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreateStatic) for xEventGroupCreateStatic
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    cmsis_os2.o(i.osEventFlagsNew) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBitsFromISR) for xEventGroupSetBitsFromISR
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    cmsis_os2.o(i.osEventFlagsSet) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osEventFlagsWait) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    cmsis_os2.o(i.osEventFlagsWait) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelGetInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    cmsis_os2.o(i.osKernelGetState) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelGetState) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetSysTimerFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetTickCount) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelLock) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelUnlock) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMessageQueueDelete) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os2.o(i.osMessageQueueGet) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMessageQueueGetCount) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMessageQueueNew) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMessageQueuePut) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMessageQueueReset) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    cmsis_os2.o(i.osMessageQueueReset) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueTakeMutexRecursive) for xQueueTakeMutexRecursive
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMutexAcquire) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMutexDelete) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMutexGetOwner) refers to queue.o(i.xQueueGetMutexHolder) for xQueueGetMutexHolder
    cmsis_os2.o(i.osMutexGetOwner) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMutexNew) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveMutexRecursive) for xQueueGiveMutexRecursive
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMutexRelease) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osSemaphoreAcquire) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreDelete) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osSemaphoreGetCount) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osSemaphoreNew) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreRelease) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetSystemState) for uxTaskGetSystemState
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osThreadEnumerate) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadExit) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsClear) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsGet) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsSet) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os2.o(i.osThreadFlagsWait) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadGetCount) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadGetCount) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadGetId) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadGetName) refers to tasks.o(i.pcTaskGetName) for pcTaskGetName
    cmsis_os2.o(i.osThreadGetName) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os2.o(i.osThreadGetPriority) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadGetStackSpace) refers to tasks.o(i.uxTaskGetStackHighWaterMark) for uxTaskGetStackHighWaterMark
    cmsis_os2.o(i.osThreadGetStackSpace) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadGetState) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadGetState) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os2.o(i.osThreadNew) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os2.o(i.osThreadResume) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os2.o(i.osThreadSetPriority) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os2.o(i.osThreadSuspend) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadTerminate) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osThreadYield) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerDelete) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osTimerGetName) refers to timers.o(i.pcTimerGetName) for pcTimerGetName
    cmsis_os2.o(i.osTimerGetName) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osTimerIsRunning) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerIsRunning) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.TimerCallback) for TimerCallback
    cmsis_os2.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStart) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStop) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.vApplicationGetIdleTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    cmsis_os2.o(i.vApplicationGetTimerTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.SysTick_Handler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvStartFirstTask
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (2 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (40 bytes).
    Removing crc.o(.rev16_text), (4 bytes).
    Removing crc.o(.revsh_text), (4 bytes).
    Removing crc.o(.rrx_text), (6 bytes).
    Removing crc.o(i.HAL_CRC_MspDeInit), (28 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (60 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (28 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (52 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt), (78 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_Enable), (124 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (88 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit), (228 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler), (220 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion), (316 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_PollForEvent), (94 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start), (192 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA), (268 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start_IT), (204 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop), (52 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (86 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_IT), (62 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (216 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (476 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (30 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (304 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (168 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (180 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (78 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (88 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (112 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (32 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (240 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (124 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_InitTick), (64 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (100 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_crc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_crc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_crc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_crc.o(i.HAL_CRC_Accumulate), (38 bytes).
    Removing stm32f1xx_hal_crc.o(i.HAL_CRC_Calculate), (46 bytes).
    Removing stm32f1xx_hal_crc.o(i.HAL_CRC_DeInit), (50 bytes).
    Removing stm32f1xx_hal_crc.o(i.HAL_CRC_GetState), (4 bytes).
    Removing stm32f1xx_hal_crc.o(i.HAL_CRC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_crc.o(i.HAL_CRC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (94 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (592 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (592 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (400 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (54 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (266 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (46 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (210 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (232 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (86 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (86 bytes).
    Removing stm32f1xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort), (288 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT), (288 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop), (66 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler), (224 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive), (342 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (236 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT), (176 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit), (366 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (276 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (164 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (204 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (148 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (144 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR), (124 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt), (106 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback), (98 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback), (112 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction), (92 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (416 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (172 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA), (144 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing motor_ctrl.o(.rev16_text), (4 bytes).
    Removing motor_ctrl.o(.revsh_text), (4 bytes).
    Removing motor_ctrl.o(.rrx_text), (6 bytes).
    Removing speed.o(.rev16_text), (4 bytes).
    Removing speed.o(.revsh_text), (4 bytes).
    Removing speed.o(.rrx_text), (6 bytes).
    Removing transmit.o(.rev16_text), (4 bytes).
    Removing transmit.o(.revsh_text), (4 bytes).
    Removing transmit.o(.rrx_text), (6 bytes).
    Removing control.o(.rev16_text), (4 bytes).
    Removing control.o(.revsh_text), (4 bytes).
    Removing control.o(.rrx_text), (6 bytes).
    Removing control.o(i.AngleCalculate), (396 bytes).
    Removing control.o(i.GetMpuData), (32 bytes).
    Removing control.o(.data), (60 bytes).
    Removing control.o(.data), (4 bytes).
    Removing control.o(.data), (1 bytes).
    Removing filter.o(i.ComplementaryFilter), (72 bytes).
    Removing filter.o(.data), (8 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(.rrx_text), (6 bytes).
    Removing mpu6050.o(i.MPU_Get_Accelerometer), (48 bytes).
    Removing mpu6050.o(i.MPU_Get_Gyroscope), (48 bytes).
    Removing mpu6050.o(i.MPU_Get_Temperature), (84 bytes).
    Removing mpu6050.o(i.MPU_Init), (120 bytes).
    Removing mpu6050.o(i.MPU_Read_Byte), (52 bytes).
    Removing mpu6050.o(i.MPU_Read_Len), (32 bytes).
    Removing mpu6050.o(i.MPU_Set_Accel_Fsr), (10 bytes).
    Removing mpu6050.o(i.MPU_Set_Gyro_Fsr), (10 bytes).
    Removing mpu6050.o(i.MPU_Set_LPF), (48 bytes).
    Removing mpu6050.o(i.MPU_Set_Rate), (46 bytes).
    Removing mpu6050.o(i.MPU_Write_Byte), (48 bytes).
    Removing mpu6050.o(i.MPU_Write_Len), (40 bytes).
    Removing nrf24l01.o(.rev16_text), (4 bytes).
    Removing nrf24l01.o(.revsh_text), (4 bytes).
    Removing nrf24l01.o(.rrx_text), (6 bytes).
    Removing nrf24l01.o(i.NRF24L01_TX_Mode), (116 bytes).
    Removing nrf24l01.o(i.NRF24L01_TxPacket), (108 bytes).
    Removing nrf24l01.o(i.NRF_LowPower_Mode), (32 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (20 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (74 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (64 bytes).
    Removing event_groups.o(i.xEventGroupClearBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (44 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(i.xEventGroupSetBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupSync), (204 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (264 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.prvInitialiseMutex), (22 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (36 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (22 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (40 bytes).
    Removing queue.o(i.vQueueDelete), (46 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (40 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphore), (58 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphoreStatic), (64 bytes).
    Removing queue.o(i.xQueueCreateMutex), (22 bytes).
    Removing queue.o(i.xQueueCreateMutexStatic), (26 bytes).
    Removing queue.o(i.xQueueGenericCreate), (72 bytes).
    Removing queue.o(i.xQueueGetMutexHolder), (28 bytes).
    Removing queue.o(i.xQueueGetMutexHolderFromISR), (30 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (156 bytes).
    Removing queue.o(i.xQueueGiveMutexRecursive), (62 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (30 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (34 bytes).
    Removing queue.o(i.xQueuePeek), (308 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (116 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (154 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (376 bytes).
    Removing queue.o(i.xQueueTakeMutexRecursive), (64 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (76 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (140 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (66 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (130 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (60 bytes).
    Removing stream_buffer.o(i.ucStreamBufferGetStreamBufferType), (8 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (34 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (82 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (114 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (34 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (48 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (212 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (144 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (68 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (236 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (142 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (38 bytes).
    Removing tasks.o(i.eTaskGetState), (104 bytes).
    Removing tasks.o(i.pcTaskGetName), (32 bytes).
    Removing tasks.o(i.prvListTasksWithinSingleList), (88 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (20 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (52 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (104 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (16 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (172 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (44 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelayUntil), (140 bytes).
    Removing tasks.o(i.vTaskDelete), (144 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (28 bytes).
    Removing tasks.o(i.vTaskGetInfo), (116 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (176 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (144 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (180 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (112 bytes).
    Removing tasks.o(i.vTaskResume), (124 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (48 bytes).
    Removing tasks.o(i.vTaskSuspend), (156 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (200 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (240 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (16 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (140 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (120 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (136 bytes).
    Removing timers.o(i.pcTimerGetName), (22 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (68 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (36 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerSetTimerID), (38 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (52 bytes).
    Removing timers.o(i.xTimerCreateStatic), (46 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (22 bytes).
    Removing timers.o(i.xTimerGetPeriod), (22 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (32 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (44 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (60 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (40 bytes).
    Removing cmsis_os2.o(.rev16_text), (4 bytes).
    Removing cmsis_os2.o(.revsh_text), (4 bytes).
    Removing cmsis_os2.o(.rrx_text), (6 bytes).
    Removing cmsis_os2.o(i.TimerCallback), (24 bytes).
    Removing cmsis_os2.o(i.osDelayUntil), (60 bytes).
    Removing cmsis_os2.o(i.osEventFlagsClear), (88 bytes).
    Removing cmsis_os2.o(i.osEventFlagsDelete), (56 bytes).
    Removing cmsis_os2.o(i.osEventFlagsGet), (48 bytes).
    Removing cmsis_os2.o(i.osEventFlagsNew), (68 bytes).
    Removing cmsis_os2.o(i.osEventFlagsSet), (108 bytes).
    Removing cmsis_os2.o(i.osEventFlagsWait), (124 bytes).
    Removing cmsis_os2.o(i.osKernelGetInfo), (60 bytes).
    Removing cmsis_os2.o(i.osKernelGetState), (32 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerCount), (48 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerFreq), (12 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickCount), (40 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickFreq), (6 bytes).
    Removing cmsis_os2.o(i.osKernelLock), (68 bytes).
    Removing cmsis_os2.o(i.osKernelRestoreLock), (92 bytes).
    Removing cmsis_os2.o(i.osKernelUnlock), (84 bytes).
    Removing cmsis_os2.o(i.osMessageQueueDelete), (68 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGet), (124 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCapacity), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCount), (44 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetMsgSize), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetSpace), (68 bytes).
    Removing cmsis_os2.o(i.osMessageQueueNew), (116 bytes).
    Removing cmsis_os2.o(i.osMessageQueuePut), (128 bytes).
    Removing cmsis_os2.o(i.osMessageQueueReset), (60 bytes).
    Removing cmsis_os2.o(i.osMutexAcquire), (108 bytes).
    Removing cmsis_os2.o(i.osMutexDelete), (68 bytes).
    Removing cmsis_os2.o(i.osMutexGetOwner), (44 bytes).
    Removing cmsis_os2.o(i.osMutexNew), (128 bytes).
    Removing cmsis_os2.o(i.osMutexRelease), (92 bytes).
    Removing cmsis_os2.o(i.osSemaphoreAcquire), (116 bytes).
    Removing cmsis_os2.o(i.osSemaphoreDelete), (68 bytes).
    Removing cmsis_os2.o(i.osSemaphoreGetCount), (44 bytes).
    Removing cmsis_os2.o(i.osSemaphoreNew), (176 bytes).
    Removing cmsis_os2.o(i.osSemaphoreRelease), (112 bytes).
    Removing cmsis_os2.o(i.osThreadEnumerate), (124 bytes).
    Removing cmsis_os2.o(i.osThreadExit), (8 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsClear), (104 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsGet), (68 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsSet), (140 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsWait), (176 bytes).
    Removing cmsis_os2.o(i.osThreadGetCount), (40 bytes).
    Removing cmsis_os2.o(i.osThreadGetId), (40 bytes).
    Removing cmsis_os2.o(i.osThreadGetName), (40 bytes).
    Removing cmsis_os2.o(i.osThreadGetPriority), (44 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSize), (4 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSpace), (40 bytes).
    Removing cmsis_os2.o(i.osThreadGetState), (80 bytes).
    Removing cmsis_os2.o(i.osThreadResume), (56 bytes).
    Removing cmsis_os2.o(i.osThreadSetPriority), (64 bytes).
    Removing cmsis_os2.o(i.osThreadSuspend), (56 bytes).
    Removing cmsis_os2.o(i.osThreadTerminate), (76 bytes).
    Removing cmsis_os2.o(i.osThreadYield), (60 bytes).
    Removing cmsis_os2.o(i.osTimerDelete), (92 bytes).
    Removing cmsis_os2.o(i.osTimerGetName), (40 bytes).
    Removing cmsis_os2.o(i.osTimerIsRunning), (40 bytes).
    Removing cmsis_os2.o(i.osTimerNew), (132 bytes).
    Removing cmsis_os2.o(i.osTimerStart), (76 bytes).
    Removing cmsis_os2.o(i.osTimerStop), (92 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (32 bytes).

797 unused section(s) (total 60931 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/crc.c                        0x00000000   Number         0  crc.o ABSOLUTE
    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_crc.c 0x00000000   Number         0  stm32f1xx_hal_crc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\crc.c                        0x00000000   Number         0  crc.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_crc.c 0x00000000   Number         0  stm32f1xx_hal_crc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\Files\Src\control.c                   0x00000000   Number         0  control.o ABSOLUTE
    ..\Files\Src\filter.c                    0x00000000   Number         0  filter.o ABSOLUTE
    ..\Files\Src\motor_ctrl.c                0x00000000   Number         0  motor_ctrl.o ABSOLUTE
    ..\Files\Src\mpu6050.c                   0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\Files\Src\nrf24l01.c                  0x00000000   Number         0  nrf24l01.o ABSOLUTE
    ..\Files\Src\speed.c                     0x00000000   Number         0  speed.o ABSOLUTE
    ..\Files\Src\transmit.c                  0x00000000   Number         0  transmit.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\\Files\\Src\\control.c                0x00000000   Number         0  control.o ABSOLUTE
    ..\\Files\\Src\\motor_ctrl.c             0x00000000   Number         0  motor_ctrl.o ABSOLUTE
    ..\\Files\\Src\\mpu6050.c                0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\Files\\Src\\nrf24l01.c               0x00000000   Number         0  nrf24l01.o ABSOLUTE
    ..\\Files\\Src\\speed.c                  0x00000000   Number         0  speed.o ABSOLUTE
    ..\\Files\\Src\\transmit.c               0x00000000   Number         0  transmit.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000188   Section      150  port.o(.emb_text)
    .text                                    0x08000220   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x08000260   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080002ea   Section       16  aeabi_memset.o(.text)
    .text                                    0x080002fa   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000348   Section        0  heapauxi.o(.text)
    .text                                    0x0800034e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080003b2   Section       68  rt_memclr.o(.text)
    .text                                    0x080003f8   Section        8  libspace.o(.text)
    .text                                    0x08000400   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800044a   Section        0  exit.o(.text)
    .text                                    0x0800045c   Section        0  sys_exit.o(.text)
    .text                                    0x08000468   Section        2  use_no_semi.o(.text)
    .text                                    0x0800046a   Section        0  indicate_semi.o(.text)
    i.ADC_ConversionStop_Disable             0x0800046c   Section        0  stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable)
    i.BusFault_Handler                       0x080004c2   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CheckSum                               0x080004c4   Section        0  transmit.o(i.CheckSum)
    i.DebugMon_Handler                       0x080004e2   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x080004e4   Section        0  main.o(i.Error_Handler)
    i.HAL_ADC_ConfigChannel                  0x080004e8   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_Init                           0x080005e4   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08000700   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_CRC_Init                           0x08000758   Section        0  stm32f1xx_hal_crc.o(i.HAL_CRC_Init)
    i.HAL_CRC_MspInit                        0x0800077c   Section        0  crc.o(i.HAL_CRC_MspInit)
    i.HAL_DMA_Abort                          0x080007a4   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080007ec   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x08000884   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x080008a8   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08000a88   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08000a94   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08000aa0   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_MspInit                        0x08000c28   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08000c88   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000c98   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000cbc   Section        0  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000d54   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000d9c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000db8   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000df8   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCCEx_PeriphCLKConfig              0x08000e1c   Section        0  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08000f08   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08001034   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08001074   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001094   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080010b4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001100   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08001420   Section        0  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x080014d4   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_TransmitReceive                0x08001554   Section        0  stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    i.HAL_TIMEx_BreakCallback                0x08001742   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08001744   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001748   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080017b0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x0800180a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x0800180c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Encoder_Init                   0x08001864   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08001908   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08001988   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08001a16   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08001a18   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08001b80   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08001bf0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08001bf2   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08001cbe   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08001d18   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08001d40   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08001d44   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08001de0   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001df4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x08001df6   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08001df8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08001dfc   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002068   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080020cc   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x0800214c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08002168   Section        0  freertos.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x0800217c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08002216   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002218   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x0800221c   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_CRC_Init                            0x0800226c   Section        0  crc.o(i.MX_CRC_Init)
    i.MX_FREERTOS_Init                       0x08002290   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x080022e4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C2_Init                           0x080023b8   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_SPI1_Init                           0x080023f8   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_TIM1_Init                           0x08002440   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x080024ac   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08002514   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART2_UART_Init                    0x080025b4   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x080025ec   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080025ee   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.NRF24L01_Check                         0x080025f0   Section        0  nrf24l01.o(i.NRF24L01_Check)
    i.NRF24L01_Init                          0x0800263c   Section        0  nrf24l01.o(i.NRF24L01_Init)
    i.NRF24L01_RX_Mode                       0x080026b4   Section        0  nrf24l01.o(i.NRF24L01_RX_Mode)
    i.NRF24L01_Read_Buf                      0x0800271c   Section        0  nrf24l01.o(i.NRF24L01_Read_Buf)
    i.NRF24L01_Read_Reg                      0x08002770   Section        0  nrf24l01.o(i.NRF24L01_Read_Reg)
    i.NRF24L01_RxPacket                      0x080027ac   Section        0  nrf24l01.o(i.NRF24L01_RxPacket)
    i.NRF24L01_SPI_Init                      0x080027e4   Section        0  nrf24l01.o(i.NRF24L01_SPI_Init)
    i.NRF24L01_Write_Buf                     0x08002810   Section        0  nrf24l01.o(i.NRF24L01_Write_Buf)
    i.NRF24L01_Write_Reg                     0x08002868   Section        0  nrf24l01.o(i.NRF24L01_Write_Reg)
    i.PID_Cal                                0x080028ac   Section        0  motor_ctrl.o(i.PID_Cal)
    i.PID_Init                               0x08002994   Section        0  motor_ctrl.o(i.PID_Init)
    i.SPI1_SetSpeed                          0x080029bc   Section        0  nrf24l01.o(i.SPI1_SetSpeed)
    SPI1_SetSpeed                            0x080029bd   Thumb Code    46  nrf24l01.o(i.SPI1_SetSpeed)
    i.SPI_EndRxTxTransaction                 0x080029f0   Section        0  stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x080029f1   Thumb Code    32  stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFlagStateUntilTimeout          0x08002a10   Section        0  stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08002a11   Thumb Code   180  stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SPIx_ReadWriteByte                     0x08002ac8   Section        0  nrf24l01.o(i.SPIx_ReadWriteByte)
    i.Servo_uart_ctrl_task                   0x08002ae8   Section        0  freertos.o(i.Servo_uart_ctrl_task)
    i.StartDefaultTask                       0x08002b10   Section        0  freertos.o(i.StartDefaultTask)
    i.SysTick_Handler                        0x08002b18   Section        0  port.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002b44   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002bbc   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM4_IRQHandler                        0x08002bc0   Section        0  stm32f1xx_it.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x08002bcc   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08002c38   Section        0  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08002c54   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08002c55   Thumb Code    74  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08002ca4   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08002cfc   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08002cfd   Thumb Code    82  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08002d54   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08002d55   Thumb Code    64  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.UART_DMAAbortOnError                   0x08002d98   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08002d99   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08002da8   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08002da9   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08002df6   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08002df7   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08002eb8   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08002eb9   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08002f70   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08002fa6   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08002fa7   Thumb Code   120  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x08003020   Section        0  stm32f1xx_it.o(i.USART2_IRQHandler)
    i.UsageFault_Handler                     0x0800302c   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x0800302e   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x0800302f   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.connect_Aux_task                       0x08003050   Section        0  freertos.o(i.connect_Aux_task)
    i.connect_with_board                     0x0800306c   Section        0  transmit.o(i.connect_with_board)
    i.get_speed                              0x080030b4   Section        0  speed.o(i.get_speed)
    i.main                                   0x080030c0   Section        0  main.o(i.main)
    i.motor_ctrl_at_TB6612                   0x08003140   Section        0  motor_ctrl.o(i.motor_ctrl_at_TB6612)
    i.motor_ctrl_task                        0x08003420   Section        0  freertos.o(i.motor_ctrl_task)
    i.osDelay                                0x08003494   Section        0  cmsis_os2.o(i.osDelay)
    i.osKernelInitialize                     0x080034c8   Section        0  cmsis_os2.o(i.osKernelInitialize)
    i.osKernelStart                          0x08003500   Section        0  cmsis_os2.o(i.osKernelStart)
    i.osThreadNew                            0x08003540   Section        0  cmsis_os2.o(i.osThreadNew)
    i.prvAddCurrentTaskToDelayedList         0x080035e4   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x080035e5   Thumb Code    84  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x08003640   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x08003641   Thumb Code   190  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x08003710   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x08003711   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCopyDataFromQueue                   0x08003768   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08003769   Thumb Code    38  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x0800378e   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x0800378f   Thumb Code   108  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x080037fa   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x080037fb   Thumb Code    52  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x08003830   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08003831   Thumb Code    66  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x0800387c   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x0800387d   Thumb Code    82  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewQueue                  0x080038dc   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x080038dd   Thumb Code    34  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x080038fe   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x080038ff   Thumb Code   156  tasks.o(i.prvInitialiseNewTask)
    i.prvInsertBlockIntoFreeList             0x0800399c   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x0800399d   Thumb Code    72  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x080039e8   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x080039e9   Thumb Code    52  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08003a20   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08003a21   Thumb Code    28  queue.o(i.prvIsQueueEmpty)
    i.prvProcessReceivedCommands             0x08003a3c   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08003a3d   Thumb Code   198  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x08003b08   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x08003b09   Thumb Code   172  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x08003bbc   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08003bbd   Thumb Code    26  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08003bdc   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08003bdd   Thumb Code    36  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08003c04   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08003c05   Thumb Code   102  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08003c70   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08003c71   Thumb Code    36  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08003c98   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08003c99   Thumb Code    32  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08003cbc   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08003cbd   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08003d28   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08003e00   Section        0  port.o(i.pxPortInitialiseStack)
    i.receieve_from_board                    0x08003e24   Section        0  transmit.o(i.receieve_from_board)
    i.remote_recieve                         0x08003e80   Section        0  motor_ctrl.o(i.remote_recieve)
    i.speed_meter_init                       0x08003f5c   Section        0  speed.o(i.speed_meter_init)
    i.uxListRemove                           0x08003f7c   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x08003fa4   Section        0  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x08003fb8   Section        0  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x08003fd0   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08003fe6   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08003fec   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x0800401c   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08004034   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08004074   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x0800409c   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x080040f8   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x0800411c   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x08004170   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueWaitForMessageRestricted         0x08004198   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x080041dc   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08004228   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08004238   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08004244   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08004274   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x080042ac   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08004334   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08004344   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x080043a8   Section        0  port.o(i.xPortStartScheduler)
    i.xQueueGenericCreateStatic              0x08004450   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x080044b8   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x08004540   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x080046a0   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueReceive                          0x0800475c   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x08004894   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08004908   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x08004962   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetSchedulerState                 0x080049b8   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x080049d4   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x080049e0   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x08004aa8   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskRemoveFromEventList               0x08004b28   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x08004b98   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08004c5c   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x08004cbc   Section        0  timers.o(i.xTimerGenericCommand)
    x$fpl$d2f                                0x08004d24   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08004d88   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08004d99   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08004ed8   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dfix                               0x08004ee8   Section       94  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x08004f46   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dmul                               0x08004f74   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080050c8   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08005164   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$dsub                               0x08005170   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08005181   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08005344   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x0800539c   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080053ab   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcheck1                            0x08005460   Section       12  fcheck1.o(x$fpl$fcheck1)
    x$fpl$ffix                               0x0800546c   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fflt                               0x080054a4   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fmul                               0x080054d4   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x080055d6   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08005662   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x0800566c   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x0800567b   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$retnan                             0x08005756   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080057ba   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scalbnf                            0x08005816   Section       76  scalbnf.o(x$fpl$scalbnf)
    x$fpl$trapveneer                         0x08005862   Section       48  trapv.o(x$fpl$trapveneer)
    x$fpl$usenofp                            0x08005892   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08005894   Section      144  freertos.o(.constdata)
    .constdata                               0x08005924   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08005924   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08005926   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08005936   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08005946   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x0800594e   Section       10  nrf24l01.o(.constdata)
    .conststring                             0x08005958   Section       52  freertos.o(.conststring)
    .data                                    0x20000000   Section        1  main.o(.data)
    .data                                    0x20000001   Section        1  freertos.o(.data)
    .data                                    0x20000004   Section       16  freertos.o(.data)
    .data                                    0x20000014   Section        8  crc.o(.data)
    .data                                    0x2000001c   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000028   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x2000002c   Section       32  motor_ctrl.o(.data)
    i                                        0x2000002c   Data           1  motor_ctrl.o(.data)
    .data                                    0x2000004c   Section        8  speed.o(.data)
    .data                                    0x20000054   Section        1  transmit.o(.data)
    .data                                    0x20000056   Section        2  nrf24l01.o(.data)
    .data                                    0x20000058   Section       60  tasks.o(.data)
    pxDelayedTaskList                        0x2000005c   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x20000060   Data           4  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000064   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x20000068   Data           4  tasks.o(.data)
    xTickCount                               0x2000006c   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000070   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000074   Data           4  tasks.o(.data)
    uxPendedTicks                            0x20000078   Data           4  tasks.o(.data)
    xYieldPending                            0x2000007c   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000080   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000084   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x20000088   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x2000008c   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000090   Data           4  tasks.o(.data)
    .data                                    0x20000094   Section       20  timers.o(.data)
    pxCurrentTimerList                       0x20000094   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x20000098   Data           4  timers.o(.data)
    xTimerQueue                              0x2000009c   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x200000a0   Data           4  timers.o(.data)
    xLastTime                                0x200000a4   Data           4  timers.o(.data)
    .data                                    0x200000a8   Section        4  cmsis_os2.o(.data)
    KernelState                              0x200000a8   Data           4  cmsis_os2.o(.data)
    .data                                    0x200000ac   Section       24  heap_4.o(.data)
    pxEnd                                    0x200000ac   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x200000b0   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x200000b4   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x200000b8   Data           4  heap_4.o(.data)
    xStart                                   0x200000bc   Data           8  heap_4.o(.data)
    .data                                    0x200000c4   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x200000c4   Data           1  port.o(.data)
    uxCriticalNesting                        0x200000c8   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x200000cc   Data           4  port.o(.data)
    .bss                                     0x200000d0   Section       33  freertos.o(.bss)
    .bss                                     0x200000f4   Section       48  adc.o(.bss)
    .bss                                     0x20000124   Section       84  i2c.o(.bss)
    .bss                                     0x20000178   Section       88  spi.o(.bss)
    .bss                                     0x200001d0   Section      216  tim.o(.bss)
    .bss                                     0x200002a8   Section       72  usart.o(.bss)
    .bss                                     0x200002f0   Section       72  stm32f1xx_hal_timebase_tim.o(.bss)
    .bss                                     0x20000338   Section       76  motor_ctrl.o(.bss)
    .bss                                     0x20000384   Section       96  transmit.o(.bss)
    buffer                                   0x20000384   Data          30  transmit.o(.bss)
    buffer                                   0x200003a2   Data          30  transmit.o(.bss)
    .bss                                     0x200003e4   Section       64  queue.o(.bss)
    .bss                                     0x20000424   Section     1220  tasks.o(.bss)
    pxReadyTasksLists                        0x20000424   Data        1120  tasks.o(.bss)
    xDelayedTaskList1                        0x20000884   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20000898   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x200008ac   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x200008c0   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x200008d4   Data          20  tasks.o(.bss)
    .bss                                     0x200008e8   Section      280  timers.o(.bss)
    xActiveTimerList1                        0x200008e8   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x200008fc   Data          20  timers.o(.bss)
    xStaticTimerQueue                        0x20000910   Data          80  timers.o(.bss)
    ucStaticTimerQueueStorage                0x20000960   Data         160  timers.o(.bss)
    .bss                                     0x20000a00   Section     1720  cmsis_os2.o(.bss)
    Idle_TCB                                 0x20000a00   Data          92  cmsis_os2.o(.bss)
    Idle_Stack                               0x20000a5c   Data         512  cmsis_os2.o(.bss)
    Timer_TCB                                0x20000c5c   Data          92  cmsis_os2.o(.bss)
    Timer_Stack                              0x20000cb8   Data        1024  cmsis_os2.o(.bss)
    .bss                                     0x200010b8   Section     3072  heap_4.o(.bss)
    ucHeap                                   0x200010b8   Data        3072  heap_4.o(.bss)
    .bss                                     0x20001cb8   Section       96  libspace.o(.bss)
    HEAP                                     0x20001d18   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20001d18   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20001f18   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20001f18   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20002318   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x08000189   Thumb Code    32  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001ad   Thumb Code    28  port.o(.emb_text)
    PendSV_Handler                           0x080001cd   Thumb Code    72  port.o(.emb_text)
    vPortGetIPSR                             0x08000219   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x08000221   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x0800023b   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x0800023d   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_memcpy                           0x08000261   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000261   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080002c7   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memset                           0x080002eb   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr4                          0x080002fb   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080002fb   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080002fb   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080002ff   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000349   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800034b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800034d   Thumb Code     2  heapauxi.o(.text)
    __aeabi_memcpy4                          0x0800034f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800034f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800034f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000397   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x080003b3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080003b3   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080003b7   Thumb Code     0  rt_memclr.o(.text)
    __user_libspace                          0x080003f9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080003f9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080003f9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000401   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800044b   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x0800045d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000469   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000469   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800046b   Thumb Code     0  indicate_semi.o(.text)
    ADC_ConversionStop_Disable               0x0800046d   Thumb Code    86  stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable)
    BusFault_Handler                         0x080004c3   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    CheckSum                                 0x080004c5   Thumb Code    30  transmit.o(i.CheckSum)
    DebugMon_Handler                         0x080004e3   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x080004e5   Thumb Code     4  main.o(i.Error_Handler)
    HAL_ADC_ConfigChannel                    0x080004e9   Thumb Code   240  stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_Init                             0x080005e5   Thumb Code   272  stm32f1xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08000701   Thumb Code    76  adc.o(i.HAL_ADC_MspInit)
    HAL_CRC_Init                             0x08000759   Thumb Code    34  stm32f1xx_hal_crc.o(i.HAL_CRC_Init)
    HAL_CRC_MspInit                          0x0800077d   Thumb Code    30  crc.o(i.HAL_CRC_MspInit)
    HAL_DMA_Abort                            0x080007a5   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080007ed   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x08000885   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080008a9   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08000a89   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000a95   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08000aa1   Thumb Code   376  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08000c29   Thumb Code    82  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08000c89   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000c99   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000cbd   Thumb Code   132  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000d55   Thumb Code    62  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000d9d   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000db9   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000df9   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCCEx_PeriphCLKConfig                0x08000e1d   Thumb Code   224  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08000f09   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08001035   Thumb Code    54  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001075   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001095   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080010b5   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001101   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08001421   Thumb Code   178  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x080014d5   Thumb Code   112  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_TransmitReceive                  0x08001555   Thumb Code   494  stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    HAL_TIMEx_BreakCallback                  0x08001743   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08001745   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08001749   Thumb Code    92  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080017b1   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x0800180b   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x0800180d   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Encoder_Init                     0x08001865   Thumb Code   164  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08001909   Thumb Code   116  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08001989   Thumb Code   142  stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08001a17   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08001a19   Thumb Code   358  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08001b81   Thumb Code    94  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08001bf1   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08001bf3   Thumb Code   204  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08001cbf   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08001d19   Thumb Code    30  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08001d41   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08001d45   Thumb Code   144  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08001de1   Thumb Code    14  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001df5   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x08001df7   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001df9   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08001dfd   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002069   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080020cd   Thumb Code   114  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x0800214d   Thumb Code    28  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002169   Thumb Code    14  freertos.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x0800217d   Thumb Code   154  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08002217   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002219   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x0800221d   Thumb Code    70  adc.o(i.MX_ADC1_Init)
    MX_CRC_Init                              0x0800226d   Thumb Code    28  crc.o(i.MX_CRC_Init)
    MX_FREERTOS_Init                         0x08002291   Thumb Code    60  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x080022e5   Thumb Code   196  gpio.o(i.MX_GPIO_Init)
    MX_I2C2_Init                             0x080023b9   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_SPI1_Init                             0x080023f9   Thumb Code    62  spi.o(i.MX_SPI1_Init)
    MX_TIM1_Init                             0x08002441   Thumb Code   100  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x080024ad   Thumb Code   100  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08002515   Thumb Code   152  tim.o(i.MX_TIM3_Init)
    MX_USART2_UART_Init                      0x080025b5   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x080025ed   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080025ef   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    NRF24L01_Check                           0x080025f1   Thumb Code    68  nrf24l01.o(i.NRF24L01_Check)
    NRF24L01_Init                            0x0800263d   Thumb Code   104  nrf24l01.o(i.NRF24L01_Init)
    NRF24L01_RX_Mode                         0x080026b5   Thumb Code    96  nrf24l01.o(i.NRF24L01_RX_Mode)
    NRF24L01_Read_Buf                        0x0800271d   Thumb Code    76  nrf24l01.o(i.NRF24L01_Read_Buf)
    NRF24L01_Read_Reg                        0x08002771   Thumb Code    52  nrf24l01.o(i.NRF24L01_Read_Reg)
    NRF24L01_RxPacket                        0x080027ad   Thumb Code    56  nrf24l01.o(i.NRF24L01_RxPacket)
    NRF24L01_SPI_Init                        0x080027e5   Thumb Code    38  nrf24l01.o(i.NRF24L01_SPI_Init)
    NRF24L01_Write_Buf                       0x08002811   Thumb Code    78  nrf24l01.o(i.NRF24L01_Write_Buf)
    NRF24L01_Write_Reg                       0x08002869   Thumb Code    58  nrf24l01.o(i.NRF24L01_Write_Reg)
    PID_Cal                                  0x080028ad   Thumb Code   220  motor_ctrl.o(i.PID_Cal)
    PID_Init                                 0x08002995   Thumb Code    34  motor_ctrl.o(i.PID_Init)
    SPIx_ReadWriteByte                       0x08002ac9   Thumb Code    32  nrf24l01.o(i.SPIx_ReadWriteByte)
    Servo_uart_ctrl_task                     0x08002ae9   Thumb Code    32  freertos.o(i.Servo_uart_ctrl_task)
    StartDefaultTask                         0x08002b11   Thumb Code     8  freertos.o(i.StartDefaultTask)
    SysTick_Handler                          0x08002b19   Thumb Code    38  port.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002b45   Thumb Code   120  main.o(i.SystemClock_Config)
    SystemInit                               0x08002bbd   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM4_IRQHandler                          0x08002bc1   Thumb Code     6  stm32f1xx_it.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x08002bcd   Thumb Code    94  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08002c39   Thumb Code    26  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x08002ca5   Thumb Code    84  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_IT                    0x08002f71   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART2_IRQHandler                        0x08003021   Thumb Code     6  stm32f1xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x0800302d   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    connect_Aux_task                         0x08003051   Thumb Code    24  freertos.o(i.connect_Aux_task)
    connect_with_board                       0x0800306d   Thumb Code    64  transmit.o(i.connect_with_board)
    get_speed                                0x080030b5   Thumb Code    12  speed.o(i.get_speed)
    main                                     0x080030c1   Thumb Code   120  main.o(i.main)
    motor_ctrl_at_TB6612                     0x08003141   Thumb Code   684  motor_ctrl.o(i.motor_ctrl_at_TB6612)
    motor_ctrl_task                          0x08003421   Thumb Code    94  freertos.o(i.motor_ctrl_task)
    osDelay                                  0x08003495   Thumb Code    46  cmsis_os2.o(i.osDelay)
    osKernelInitialize                       0x080034c9   Thumb Code    50  cmsis_os2.o(i.osKernelInitialize)
    osKernelStart                            0x08003501   Thumb Code    58  cmsis_os2.o(i.osKernelStart)
    osThreadNew                              0x08003541   Thumb Code   160  cmsis_os2.o(i.osThreadNew)
    pvPortMalloc                             0x08003d29   Thumb Code   210  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08003e01   Thumb Code    32  port.o(i.pxPortInitialiseStack)
    receieve_from_board                      0x08003e25   Thumb Code    76  transmit.o(i.receieve_from_board)
    remote_recieve                           0x08003e81   Thumb Code   184  motor_ctrl.o(i.remote_recieve)
    speed_meter_init                         0x08003f5d   Thumb Code    22  speed.o(i.speed_meter_init)
    uxListRemove                             0x08003f7d   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x08003fa5   Thumb Code    16  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x08003fb9   Thumb Code    18  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x08003fd1   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08003fe7   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08003fed   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x0800401d   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08004035   Thumb Code    54  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08004075   Thumb Code    34  port.o(i.vPortExitCritical)
    vPortFree                                0x0800409d   Thumb Code    88  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x080040f9   Thumb Code    32  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x0800411d   Thumb Code    74  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x08004171   Thumb Code    34  queue.o(i.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x08004199   Thumb Code    68  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x080041dd   Thumb Code    66  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08004229   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08004239   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08004245   Thumb Code    44  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08004275   Thumb Code    52  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x080042ad   Thumb Code   118  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08004335   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08004345   Thumb Code    90  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x080043a9   Thumb Code   154  port.o(i.xPortStartScheduler)
    xQueueGenericCreateStatic                0x08004451   Thumb Code   102  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x080044b9   Thumb Code   132  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x08004541   Thumb Code   346  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x080046a1   Thumb Code   188  queue.o(i.xQueueGenericSendFromISR)
    xQueueReceive                            0x0800475d   Thumb Code   308  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x08004895   Thumb Code   112  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08004909   Thumb Code    90  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x08004963   Thumb Code    86  tasks.o(i.xTaskCreateStatic)
    xTaskGetSchedulerState                   0x080049b9   Thumb Code    22  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x080049d5   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x080049e1   Thumb Code   190  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x08004aa9   Thumb Code   118  tasks.o(i.xTaskPriorityDisinherit)
    xTaskRemoveFromEventList                 0x08004b29   Thumb Code    98  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x08004b99   Thumb Code   182  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08004c5d   Thumb Code    78  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x08004cbd   Thumb Code    98  timers.o(i.xTimerGenericCommand)
    __aeabi_d2f                              0x08004d25   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08004d25   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08004d89   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08004d89   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08004ed9   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_d2iz                             0x08004ee9   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08004ee9   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x08004f47   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08004f47   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_dmul                             0x08004f75   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08004f75   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080050c9   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08005165   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_dsub                             0x08005171   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08005171   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08005345   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08005345   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x0800539d   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x0800539d   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcheck_NaN1                        0x08005461   Thumb Code     6  fcheck1.o(x$fpl$fcheck1)
    __aeabi_f2iz                             0x0800546d   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x0800546d   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_i2f                              0x080054a5   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x080054a5   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_fmul                             0x080054d5   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080054d5   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x080055d7   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08005663   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x0800566d   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x0800566d   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __fpl_return_NaN                         0x08005757   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080057bb   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __ARM_scalbnf                            0x08005817   Thumb Code    76  scalbnf.o(x$fpl$scalbnf)
    __fpl_cmpreturn                          0x08005863   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08005892   Number         0  usenofp.o(x$fpl$usenofp)
    defaultTask_attributes                   0x08005894   Data          36  freertos.o(.constdata)
    Servo_uart_ctrl_attributes               0x080058b8   Data          36  freertos.o(.constdata)
    connect_Aux_attributes                   0x080058dc   Data          36  freertos.o(.constdata)
    motor_ctrl_attributes                    0x08005900   Data          36  freertos.o(.constdata)
    AHBPrescTable                            0x08005936   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08005946   Data           8  system_stm32f1xx.o(.constdata)
    TX_ADDRESS                               0x0800594e   Data           5  nrf24l01.o(.constdata)
    RX_ADDRESS                               0x08005953   Data           5  nrf24l01.o(.constdata)
    Region$$Table$$Base                      0x0800598c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080059ac   Number         0  anon$$obj.o(Region$$Table)
    NRF_OK                                   0x20000000   Data           1  main.o(.data)
    CRC_test                                 0x20000001   Data           1  freertos.o(.data)
    defaultTaskHandle                        0x20000004   Data           4  freertos.o(.data)
    Servo_uart_ctrlHandle                    0x20000008   Data           4  freertos.o(.data)
    connect_AuxHandle                        0x2000000c   Data           4  freertos.o(.data)
    motor_ctrlHandle                         0x20000010   Data           4  freertos.o(.data)
    hcrc                                     0x20000014   Data           8  crc.o(.data)
    uwTickFreq                               0x2000001c   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000020   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000024   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x20000028   Data           4  system_stm32f1xx.o(.data)
    X_expect                                 0x2000002e   Data           2  motor_ctrl.o(.data)
    Y_expect                                 0x20000030   Data           2  motor_ctrl.o(.data)
    Z_expect                                 0x20000032   Data           2  motor_ctrl.o(.data)
    height_expect                            0x20000034   Data           2  motor_ctrl.o(.data)
    kp                                       0x20000038   Data           4  motor_ctrl.o(.data)
    ki                                       0x2000003c   Data           4  motor_ctrl.o(.data)
    kd                                       0x20000040   Data           4  motor_ctrl.o(.data)
    positive                                 0x20000044   Data           4  motor_ctrl.o(.data)
    float_height                             0x20000048   Data           4  motor_ctrl.o(.data)
    motor_speed                              0x2000004c   Data           8  speed.o(.data)
    OK                                       0x20000054   Data           1  transmit.o(.data)
    countt                                   0x20000056   Data           2  nrf24l01.o(.data)
    pxCurrentTCB                             0x20000058   Data           4  tasks.o(.data)
    tmp_buf                                  0x200000d0   Data          33  freertos.o(.bss)
    hadc1                                    0x200000f4   Data          48  adc.o(.bss)
    hi2c2                                    0x20000124   Data          84  i2c.o(.bss)
    hspi1                                    0x20000178   Data          88  spi.o(.bss)
    htim1                                    0x200001d0   Data          72  tim.o(.bss)
    htim2                                    0x20000218   Data          72  tim.o(.bss)
    htim3                                    0x20000260   Data          72  tim.o(.bss)
    huart2                                   0x200002a8   Data          72  usart.o(.bss)
    htim4                                    0x200002f0   Data          72  stm32f1xx_hal_timebase_tim.o(.bss)
    remote                                   0x20000338   Data          12  motor_ctrl.o(.bss)
    motor                                    0x20000344   Data          64  motor_ctrl.o(.bss)
    main_board                               0x200003c0   Data          18  transmit.o(.bss)
    auxilliary_board                         0x200003d2   Data          18  transmit.o(.bss)
    xQueueRegistry                           0x200003e4   Data          64  queue.o(.bss)
    __libspace_start                         0x20001cb8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20001d18   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005a7c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000059ac, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         6253  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         6533    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         6535    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         6537    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000002   Code   RO         6402    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6408    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6410    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6413    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6415    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6417    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6420    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6422    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6424    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6426    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6428    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6430    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6432    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6434    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6436    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6438    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6440    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6444    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6446    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6448    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         6450    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000002   Code   RO         6451    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x08000164   0x00000002   Code   RO         6471    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6484    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6486    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6488    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6491    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6494    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6496    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         6499    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000002   Code   RO         6500    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000168   0x08000168   0x00000000   Code   RO         6315    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x08000168   0x00000000   Code   RO         6363    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x08000168   0x00000006   Code   RO         6375    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x0800016e   0x00000000   Code   RO         6365    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x0800016e   0x00000004   Code   RO         6366    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         6368    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000008   Code   RO         6369    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x0800017a   0x00000002   Code   RO         6405    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         6453    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x0800017c   0x00000004   Code   RO         6454    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x08000180   0x00000006   Code   RO         6455    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x00000096   Code   RO         6162    .emb_text           port.o
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000260   0x08000260   0x0000008a   Code   RO         6243    .text               c_w.l(rt_memcpy_v6.o)
    0x080002ea   0x080002ea   0x00000010   Code   RO         6247    .text               c_w.l(aeabi_memset.o)
    0x080002fa   0x080002fa   0x0000004e   Code   RO         6249    .text               c_w.l(rt_memclr_w.o)
    0x08000348   0x08000348   0x00000006   Code   RO         6251    .text               c_w.l(heapauxi.o)
    0x0800034e   0x0800034e   0x00000064   Code   RO         6318    .text               c_w.l(rt_memcpy_w.o)
    0x080003b2   0x080003b2   0x00000044   Code   RO         6320    .text               c_w.l(rt_memclr.o)
    0x080003f6   0x080003f6   0x00000002   PAD
    0x080003f8   0x080003f8   0x00000008   Code   RO         6390    .text               c_w.l(libspace.o)
    0x08000400   0x08000400   0x0000004a   Code   RO         6393    .text               c_w.l(sys_stackheap_outer.o)
    0x0800044a   0x0800044a   0x00000012   Code   RO         6395    .text               c_w.l(exit.o)
    0x0800045c   0x0800045c   0x0000000c   Code   RO         6463    .text               c_w.l(sys_exit.o)
    0x08000468   0x08000468   0x00000002   Code   RO         6474    .text               c_w.l(use_no_semi.o)
    0x0800046a   0x0800046a   0x00000000   Code   RO         6476    .text               c_w.l(indicate_semi.o)
    0x0800046a   0x0800046a   0x00000002   PAD
    0x0800046c   0x0800046c   0x00000056   Code   RO          784    i.ADC_ConversionStop_Disable  stm32f1xx_hal_adc.o
    0x080004c2   0x080004c2   0x00000002   Code   RO          615    i.BusFault_Handler  stm32f1xx_it.o
    0x080004c4   0x080004c4   0x0000001e   Code   RO         4316    i.CheckSum          transmit.o
    0x080004e2   0x080004e2   0x00000002   Code   RO          616    i.DebugMon_Handler  stm32f1xx_it.o
    0x080004e4   0x080004e4   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080004e8   0x080004e8   0x000000fc   Code   RO          790    i.HAL_ADC_ConfigChannel  stm32f1xx_hal_adc.o
    0x080005e4   0x080005e4   0x0000011c   Code   RO          799    i.HAL_ADC_Init      stm32f1xx_hal_adc.o
    0x08000700   0x08000700   0x00000058   Code   RO          334    i.HAL_ADC_MspInit   adc.o
    0x08000758   0x08000758   0x00000022   Code   RO         2041    i.HAL_CRC_Init      stm32f1xx_hal_crc.o
    0x0800077a   0x0800077a   0x00000002   PAD
    0x0800077c   0x0800077c   0x00000028   Code   RO          376    i.HAL_CRC_MspInit   crc.o
    0x080007a4   0x080007a4   0x00000046   Code   RO         1441    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x080007ea   0x080007ea   0x00000002   PAD
    0x080007ec   0x080007ec   0x00000098   Code   RO         1442    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08000884   0x08000884   0x00000024   Code   RO         1067    i.HAL_Delay         stm32f1xx_hal.o
    0x080008a8   0x080008a8   0x000001e0   Code   RO         1377    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08000a88   0x08000a88   0x0000000a   Code   RO         1381    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08000a92   0x08000a92   0x00000002   PAD
    0x08000a94   0x08000a94   0x0000000c   Code   RO         1071    i.HAL_GetTick       stm32f1xx_hal.o
    0x08000aa0   0x08000aa0   0x00000188   Code   RO         2108    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x08000c28   0x08000c28   0x00000060   Code   RO          418    i.HAL_I2C_MspInit   i2c.o
    0x08000c88   0x08000c88   0x00000010   Code   RO         1077    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000c98   0x08000c98   0x00000024   Code   RO         1078    i.HAL_Init          stm32f1xx_hal.o
    0x08000cbc   0x08000cbc   0x00000098   Code   RO          709    i.HAL_InitTick      stm32f1xx_hal_timebase_tim.o
    0x08000d54   0x08000d54   0x00000048   Code   RO          685    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000d9c   0x08000d9c   0x0000001a   Code   RO         1537    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08000db6   0x08000db6   0x00000002   PAD
    0x08000db8   0x08000db8   0x00000040   Code   RO         1543    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08000df8   0x08000df8   0x00000024   Code   RO         1544    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08000e1c   0x08000e1c   0x000000ec   Code   RO         1339    i.HAL_RCCEx_PeriphCLKConfig  stm32f1xx_hal_rcc_ex.o
    0x08000f08   0x08000f08   0x0000012c   Code   RO         1235    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08001034   0x08001034   0x00000040   Code   RO         1239    i.HAL_RCC_GetClockConfig  stm32f1xx_hal_rcc.o
    0x08001074   0x08001074   0x00000020   Code   RO         1242    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08001094   0x08001094   0x00000020   Code   RO         1243    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080010b4   0x080010b4   0x0000004c   Code   RO         1244    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08001100   0x08001100   0x00000320   Code   RO         1247    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001420   0x08001420   0x000000b2   Code   RO         2545    i.HAL_SPI_Init      stm32f1xx_hal_spi.o
    0x080014d2   0x080014d2   0x00000002   PAD
    0x080014d4   0x080014d4   0x00000080   Code   RO          460    i.HAL_SPI_MspInit   spi.o
    0x08001554   0x08001554   0x000001ee   Code   RO         2554    i.HAL_SPI_TransmitReceive  stm32f1xx_hal_spi.o
    0x08001742   0x08001742   0x00000002   Code   RO         3562    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08001744   0x08001744   0x00000002   Code   RO         3563    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08001746   0x08001746   0x00000002   PAD
    0x08001748   0x08001748   0x00000068   Code   RO         3581    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x080017b0   0x080017b0   0x0000005a   Code   RO         2858    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x0800180a   0x0800180a   0x00000002   Code   RO         2860    i.HAL_TIM_Base_MspInit  stm32f1xx_hal_tim.o
    0x0800180c   0x0800180c   0x00000058   Code   RO         2863    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08001864   0x08001864   0x000000a4   Code   RO         2879    i.HAL_TIM_Encoder_Init  stm32f1xx_hal_tim.o
    0x08001908   0x08001908   0x00000080   Code   RO          502    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08001988   0x08001988   0x0000008e   Code   RO         2882    i.HAL_TIM_Encoder_Start  stm32f1xx_hal_tim.o
    0x08001a16   0x08001a16   0x00000002   Code   RO         2892    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08001a18   0x08001a18   0x00000166   Code   RO         2906    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08001b7e   0x08001b7e   0x00000002   PAD
    0x08001b80   0x08001b80   0x00000070   Code   RO          503    i.HAL_TIM_MspPostInit  tim.o
    0x08001bf0   0x08001bf0   0x00000002   Code   RO         2909    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08001bf2   0x08001bf2   0x000000cc   Code   RO         2930    i.HAL_TIM_PWM_ConfigChannel  stm32f1xx_hal_tim.o
    0x08001cbe   0x08001cbe   0x0000005a   Code   RO         2933    i.HAL_TIM_PWM_Init  stm32f1xx_hal_tim.o
    0x08001d18   0x08001d18   0x00000028   Code   RO          505    i.HAL_TIM_PWM_MspInit  tim.o
    0x08001d40   0x08001d40   0x00000002   Code   RO         2936    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08001d42   0x08001d42   0x00000002   PAD
    0x08001d44   0x08001d44   0x0000009c   Code   RO         2938    i.HAL_TIM_PWM_Start  stm32f1xx_hal_tim.o
    0x08001de0   0x08001de0   0x00000014   Code   RO           14    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x08001df4   0x08001df4   0x00000002   Code   RO         2949    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08001df6   0x08001df6   0x00000002   Code   RO         3841    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x08001df8   0x08001df8   0x00000002   Code   RO         3855    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x08001dfa   0x08001dfa   0x00000002   PAD
    0x08001dfc   0x08001dfc   0x0000026c   Code   RO         3858    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08002068   0x08002068   0x00000064   Code   RO         3859    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x080020cc   0x080020cc   0x00000080   Code   RO          574    i.HAL_UART_MspInit  usart.o
    0x0800214c   0x0800214c   0x0000001c   Code   RO         3864    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x08002168   0x08002168   0x00000014   Code   RO          247    i.HAL_UART_RxCpltCallback  freertos.o
    0x0800217c   0x0800217c   0x0000009a   Code   RO         3867    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x08002216   0x08002216   0x00000002   Code   RO         3870    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x08002218   0x08002218   0x00000002   Code   RO          617    i.HardFault_Handler  stm32f1xx_it.o
    0x0800221a   0x0800221a   0x00000002   PAD
    0x0800221c   0x0800221c   0x00000050   Code   RO          335    i.MX_ADC1_Init      adc.o
    0x0800226c   0x0800226c   0x00000024   Code   RO          377    i.MX_CRC_Init       crc.o
    0x08002290   0x08002290   0x00000054   Code   RO          248    i.MX_FREERTOS_Init  freertos.o
    0x080022e4   0x080022e4   0x000000d4   Code   RO          223    i.MX_GPIO_Init      gpio.o
    0x080023b8   0x080023b8   0x00000040   Code   RO          419    i.MX_I2C2_Init      i2c.o
    0x080023f8   0x080023f8   0x00000048   Code   RO          461    i.MX_SPI1_Init      spi.o
    0x08002440   0x08002440   0x0000006c   Code   RO          506    i.MX_TIM1_Init      tim.o
    0x080024ac   0x080024ac   0x00000068   Code   RO          507    i.MX_TIM2_Init      tim.o
    0x08002514   0x08002514   0x000000a0   Code   RO          508    i.MX_TIM3_Init      tim.o
    0x080025b4   0x080025b4   0x00000038   Code   RO          575    i.MX_USART2_UART_Init  usart.o
    0x080025ec   0x080025ec   0x00000002   Code   RO          618    i.MemManage_Handler  stm32f1xx_it.o
    0x080025ee   0x080025ee   0x00000002   Code   RO          619    i.NMI_Handler       stm32f1xx_it.o
    0x080025f0   0x080025f0   0x0000004c   Code   RO         4518    i.NRF24L01_Check    nrf24l01.o
    0x0800263c   0x0800263c   0x00000078   Code   RO         4519    i.NRF24L01_Init     nrf24l01.o
    0x080026b4   0x080026b4   0x00000068   Code   RO         4520    i.NRF24L01_RX_Mode  nrf24l01.o
    0x0800271c   0x0800271c   0x00000054   Code   RO         4521    i.NRF24L01_Read_Buf  nrf24l01.o
    0x08002770   0x08002770   0x0000003c   Code   RO         4522    i.NRF24L01_Read_Reg  nrf24l01.o
    0x080027ac   0x080027ac   0x00000038   Code   RO         4523    i.NRF24L01_RxPacket  nrf24l01.o
    0x080027e4   0x080027e4   0x0000002c   Code   RO         4524    i.NRF24L01_SPI_Init  nrf24l01.o
    0x08002810   0x08002810   0x00000058   Code   RO         4527    i.NRF24L01_Write_Buf  nrf24l01.o
    0x08002868   0x08002868   0x00000044   Code   RO         4528    i.NRF24L01_Write_Reg  nrf24l01.o
    0x080028ac   0x080028ac   0x000000e8   Code   RO         4230    i.PID_Cal           motor_ctrl.o
    0x08002994   0x08002994   0x00000028   Code   RO         4231    i.PID_Init          motor_ctrl.o
    0x080029bc   0x080029bc   0x00000034   Code   RO         4530    i.SPI1_SetSpeed     nrf24l01.o
    0x080029f0   0x080029f0   0x00000020   Code   RO         2583    i.SPI_EndRxTxTransaction  stm32f1xx_hal_spi.o
    0x08002a10   0x08002a10   0x000000b8   Code   RO         2588    i.SPI_WaitFlagStateUntilTimeout  stm32f1xx_hal_spi.o
    0x08002ac8   0x08002ac8   0x00000020   Code   RO         4531    i.SPIx_ReadWriteByte  nrf24l01.o
    0x08002ae8   0x08002ae8   0x00000028   Code   RO          249    i.Servo_uart_ctrl_task  freertos.o
    0x08002b10   0x08002b10   0x00000008   Code   RO          250    i.StartDefaultTask  freertos.o
    0x08002b18   0x08002b18   0x0000002c   Code   RO         6163    i.SysTick_Handler   port.o
    0x08002b44   0x08002b44   0x00000078   Code   RO           15    i.SystemClock_Config  main.o
    0x08002bbc   0x08002bbc   0x00000002   Code   RO         4193    i.SystemInit        system_stm32f1xx.o
    0x08002bbe   0x08002bbe   0x00000002   PAD
    0x08002bc0   0x08002bc0   0x0000000c   Code   RO          620    i.TIM4_IRQHandler   stm32f1xx_it.o
    0x08002bcc   0x08002bcc   0x0000006c   Code   RO         2951    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08002c38   0x08002c38   0x0000001a   Code   RO         2952    i.TIM_CCxChannelCmd  stm32f1xx_hal_tim.o
    0x08002c52   0x08002c52   0x00000002   PAD
    0x08002c54   0x08002c54   0x00000050   Code   RO         2964    i.TIM_OC1_SetConfig  stm32f1xx_hal_tim.o
    0x08002ca4   0x08002ca4   0x00000058   Code   RO         2965    i.TIM_OC2_SetConfig  stm32f1xx_hal_tim.o
    0x08002cfc   0x08002cfc   0x00000058   Code   RO         2966    i.TIM_OC3_SetConfig  stm32f1xx_hal_tim.o
    0x08002d54   0x08002d54   0x00000044   Code   RO         2967    i.TIM_OC4_SetConfig  stm32f1xx_hal_tim.o
    0x08002d98   0x08002d98   0x00000010   Code   RO         3872    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08002da8   0x08002da8   0x0000004e   Code   RO         3882    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08002df6   0x08002df6   0x000000c2   Code   RO         3884    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08002eb8   0x08002eb8   0x000000b8   Code   RO         3885    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08002f70   0x08002f70   0x00000036   Code   RO         3887    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x08002fa6   0x08002fa6   0x00000078   Code   RO         3888    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x0800301e   0x0800301e   0x00000002   PAD
    0x08003020   0x08003020   0x0000000c   Code   RO          621    i.USART2_IRQHandler  stm32f1xx_it.o
    0x0800302c   0x0800302c   0x00000002   Code   RO          622    i.UsageFault_Handler  stm32f1xx_it.o
    0x0800302e   0x0800302e   0x00000020   Code   RO         1550    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x0800304e   0x0800304e   0x00000002   PAD
    0x08003050   0x08003050   0x0000001c   Code   RO          251    i.connect_Aux_task  freertos.o
    0x0800306c   0x0800306c   0x00000048   Code   RO         4317    i.connect_with_board  transmit.o
    0x080030b4   0x080030b4   0x0000000c   Code   RO         4280    i.get_speed         speed.o
    0x080030c0   0x080030c0   0x00000080   Code   RO           16    i.main              main.o
    0x08003140   0x08003140   0x000002e0   Code   RO         4232    i.motor_ctrl_at_TB6612  motor_ctrl.o
    0x08003420   0x08003420   0x00000074   Code   RO          252    i.motor_ctrl_task   freertos.o
    0x08003494   0x08003494   0x00000034   Code   RO         5692    i.osDelay           cmsis_os2.o
    0x080034c8   0x080034c8   0x00000038   Code   RO         5706    i.osKernelInitialize  cmsis_os2.o
    0x08003500   0x08003500   0x00000040   Code   RO         5709    i.osKernelStart     cmsis_os2.o
    0x08003540   0x08003540   0x000000a4   Code   RO         5743    i.osThreadNew       cmsis_os2.o
    0x080035e4   0x080035e4   0x0000005c   Code   RO         5188    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x08003640   0x08003640   0x000000d0   Code   RO         5189    i.prvAddNewTaskToReadyList  tasks.o
    0x08003710   0x08003710   0x00000058   Code   RO         5538    i.prvCheckForValidListAndQueue  timers.o
    0x08003768   0x08003768   0x00000026   Code   RO         4791    i.prvCopyDataFromQueue  queue.o
    0x0800378e   0x0800378e   0x0000006c   Code   RO         4792    i.prvCopyDataToQueue  queue.o
    0x080037fa   0x080037fa   0x00000034   Code   RO         5190    i.prvDeleteTCB      tasks.o
    0x0800382e   0x0800382e   0x00000002   PAD
    0x08003830   0x08003830   0x0000004c   Code   RO         6113    i.prvHeapInit       heap_4.o
    0x0800387c   0x0800387c   0x00000060   Code   RO         5191    i.prvIdleTask       tasks.o
    0x080038dc   0x080038dc   0x00000022   Code   RO         4794    i.prvInitialiseNewQueue  queue.o
    0x080038fe   0x080038fe   0x0000009c   Code   RO         5192    i.prvInitialiseNewTask  tasks.o
    0x0800399a   0x0800399a   0x00000002   PAD
    0x0800399c   0x0800399c   0x0000004c   Code   RO         6114    i.prvInsertBlockIntoFreeList  heap_4.o
    0x080039e8   0x080039e8   0x00000038   Code   RO         5540    i.prvInsertTimerInActiveList  timers.o
    0x08003a20   0x08003a20   0x0000001c   Code   RO         4795    i.prvIsQueueEmpty   queue.o
    0x08003a3c   0x08003a3c   0x000000cc   Code   RO         5541    i.prvProcessReceivedCommands  timers.o
    0x08003b08   0x08003b08   0x000000b4   Code   RO         5542    i.prvProcessTimerOrBlockTask  timers.o
    0x08003bbc   0x08003bbc   0x00000020   Code   RO         5194    i.prvResetNextTaskUnblockTime  tasks.o
    0x08003bdc   0x08003bdc   0x00000028   Code   RO         5543    i.prvSampleTimeNow  timers.o
    0x08003c04   0x08003c04   0x0000006c   Code   RO         5544    i.prvSwitchTimerLists  timers.o
    0x08003c70   0x08003c70   0x00000028   Code   RO         6164    i.prvTaskExitError  port.o
    0x08003c98   0x08003c98   0x00000024   Code   RO         5545    i.prvTimerTask      timers.o
    0x08003cbc   0x08003cbc   0x0000006a   Code   RO         4796    i.prvUnlockQueue    queue.o
    0x08003d26   0x08003d26   0x00000002   PAD
    0x08003d28   0x08003d28   0x000000d8   Code   RO         6115    i.pvPortMalloc      heap_4.o
    0x08003e00   0x08003e00   0x00000024   Code   RO         6165    i.pxPortInitialiseStack  port.o
    0x08003e24   0x08003e24   0x0000005c   Code   RO         4318    i.receieve_from_board  transmit.o
    0x08003e80   0x08003e80   0x000000dc   Code   RO         4233    i.remote_recieve    motor_ctrl.o
    0x08003f5c   0x08003f5c   0x00000020   Code   RO         4281    i.speed_meter_init  speed.o
    0x08003f7c   0x08003f7c   0x00000026   Code   RO         4751    i.uxListRemove      list.o
    0x08003fa2   0x08003fa2   0x00000002   PAD
    0x08003fa4   0x08003fa4   0x00000014   Code   RO         5755    i.vApplicationGetIdleTaskMemory  cmsis_os2.o
    0x08003fb8   0x08003fb8   0x00000018   Code   RO         5756    i.vApplicationGetTimerTaskMemory  cmsis_os2.o
    0x08003fd0   0x08003fd0   0x00000016   Code   RO         4752    i.vListInitialise   list.o
    0x08003fe6   0x08003fe6   0x00000006   Code   RO         4753    i.vListInitialiseItem  list.o
    0x08003fec   0x08003fec   0x00000030   Code   RO         4754    i.vListInsert       list.o
    0x0800401c   0x0800401c   0x00000018   Code   RO         4755    i.vListInsertEnd    list.o
    0x08004034   0x08004034   0x00000040   Code   RO         6167    i.vPortEnterCritical  port.o
    0x08004074   0x08004074   0x00000028   Code   RO         6168    i.vPortExitCritical  port.o
    0x0800409c   0x0800409c   0x0000005c   Code   RO         6116    i.vPortFree         heap_4.o
    0x080040f8   0x080040f8   0x00000024   Code   RO         6169    i.vPortSetupTimerInterrupt  port.o
    0x0800411c   0x0800411c   0x00000054   Code   RO         6170    i.vPortValidateInterruptPriority  port.o
    0x08004170   0x08004170   0x00000028   Code   RO         4802    i.vQueueAddToRegistry  queue.o
    0x08004198   0x08004198   0x00000044   Code   RO         4806    i.vQueueWaitForMessageRestricted  queue.o
    0x080041dc   0x080041dc   0x0000004c   Code   RO         5206    i.vTaskDelay        tasks.o
    0x08004228   0x08004228   0x00000010   Code   RO         5211    i.vTaskInternalSetTimeOutState  tasks.o
    0x08004238   0x08004238   0x0000000c   Code   RO         5212    i.vTaskMissedYield  tasks.o
    0x08004244   0x08004244   0x00000030   Code   RO         5214    i.vTaskPlaceOnEventList  tasks.o
    0x08004274   0x08004274   0x00000038   Code   RO         5215    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x080042ac   0x080042ac   0x00000088   Code   RO         5223    i.vTaskStartScheduler  tasks.o
    0x08004334   0x08004334   0x00000010   Code   RO         5225    i.vTaskSuspendAll   tasks.o
    0x08004344   0x08004344   0x00000064   Code   RO         5226    i.vTaskSwitchContext  tasks.o
    0x080043a8   0x080043a8   0x000000a8   Code   RO         6171    i.xPortStartScheduler  port.o
    0x08004450   0x08004450   0x00000066   Code   RO         4812    i.xQueueGenericCreateStatic  queue.o
    0x080044b6   0x080044b6   0x00000002   PAD
    0x080044b8   0x080044b8   0x00000088   Code   RO         4813    i.xQueueGenericReset  queue.o
    0x08004540   0x08004540   0x00000160   Code   RO         4814    i.xQueueGenericSend  queue.o
    0x080046a0   0x080046a0   0x000000bc   Code   RO         4815    i.xQueueGenericSendFromISR  queue.o
    0x0800475c   0x0800475c   0x00000138   Code   RO         4824    i.xQueueReceive     queue.o
    0x08004894   0x08004894   0x00000074   Code   RO         5227    i.xTaskCheckForTimeOut  tasks.o
    0x08004908   0x08004908   0x0000005a   Code   RO         5228    i.xTaskCreate       tasks.o
    0x08004962   0x08004962   0x00000056   Code   RO         5229    i.xTaskCreateStatic  tasks.o
    0x080049b8   0x080049b8   0x0000001c   Code   RO         5233    i.xTaskGetSchedulerState  tasks.o
    0x080049d4   0x080049d4   0x0000000c   Code   RO         5234    i.xTaskGetTickCount  tasks.o
    0x080049e0   0x080049e0   0x000000c8   Code   RO         5236    i.xTaskIncrementTick  tasks.o
    0x08004aa8   0x08004aa8   0x00000080   Code   RO         5239    i.xTaskPriorityDisinherit  tasks.o
    0x08004b28   0x08004b28   0x00000070   Code   RO         5241    i.xTaskRemoveFromEventList  tasks.o
    0x08004b98   0x08004b98   0x000000c4   Code   RO         5242    i.xTaskResumeAll    tasks.o
    0x08004c5c   0x08004c5c   0x00000060   Code   RO         5552    i.xTimerCreateTimerTask  timers.o
    0x08004cbc   0x08004cbc   0x00000068   Code   RO         5553    i.xTimerGenericCommand  timers.o
    0x08004d24   0x08004d24   0x00000062   Code   RO         6255    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08004d86   0x08004d86   0x00000002   PAD
    0x08004d88   0x08004d88   0x00000150   Code   RO         6257    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08004ed8   0x08004ed8   0x00000010   Code   RO         6322    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08004ee8   0x08004ee8   0x0000005e   Code   RO         6267    x$fpl$dfix          fz_ws.l(dfix.o)
    0x08004f46   0x08004f46   0x0000002e   Code   RO         6272    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x08004f74   0x08004f74   0x00000154   Code   RO         6277    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080050c8   0x080050c8   0x0000009c   Code   RO         6326    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08005164   0x08005164   0x0000000c   Code   RO         6328    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08005170   0x08005170   0x000001d4   Code   RO         6259    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08005344   0x08005344   0x00000056   Code   RO         6279    x$fpl$f2d           fz_ws.l(f2d.o)
    0x0800539a   0x0800539a   0x00000002   PAD
    0x0800539c   0x0800539c   0x000000c4   Code   RO         6281    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08005460   0x08005460   0x0000000c   Code   RO         6330    x$fpl$fcheck1       fz_ws.l(fcheck1.o)
    0x0800546c   0x0800546c   0x00000036   Code   RO         6291    x$fpl$ffix          fz_ws.l(ffix.o)
    0x080054a2   0x080054a2   0x00000002   PAD
    0x080054a4   0x080054a4   0x00000030   Code   RO         6296    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x080054d4   0x080054d4   0x00000102   Code   RO         6301    x$fpl$fmul          fz_ws.l(fmul.o)
    0x080055d6   0x080055d6   0x0000008c   Code   RO         6332    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08005662   0x08005662   0x0000000a   Code   RO         6334    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x0800566c   0x0800566c   0x000000ea   Code   RO         6283    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08005756   0x08005756   0x00000064   Code   RO         6384    x$fpl$retnan        fz_ws.l(retnan.o)
    0x080057ba   0x080057ba   0x0000005c   Code   RO         6303    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08005816   0x08005816   0x0000004c   Code   RO         6305    x$fpl$scalbnf       fz_ws.l(scalbnf.o)
    0x08005862   0x08005862   0x00000030   Code   RO         6403    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08005892   0x08005892   0x00000000   Code   RO         6336    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08005892   0x08005892   0x00000002   PAD
    0x08005894   0x08005894   0x00000090   Data   RO          254    .constdata          freertos.o
    0x08005924   0x08005924   0x00000012   Data   RO         1248    .constdata          stm32f1xx_hal_rcc.o
    0x08005936   0x08005936   0x00000010   Data   RO         4194    .constdata          system_stm32f1xx.o
    0x08005946   0x08005946   0x00000008   Data   RO         4195    .constdata          system_stm32f1xx.o
    0x0800594e   0x0800594e   0x0000000a   Data   RO         4532    .constdata          nrf24l01.o
    0x08005958   0x08005958   0x00000034   Data   RO          255    .conststring        freertos.o
    0x0800598c   0x0800598c   0x00000020   Data   RO         6531    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080059ac, Size: 0x00002318, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080059ac   0x00000001   Data   RW           18    .data               main.o
    0x20000001   0x080059ad   0x00000001   Data   RW          256    .data               freertos.o
    0x20000002   0x080059ae   0x00000002   PAD
    0x20000004   0x080059b0   0x00000010   Data   RW          257    .data               freertos.o
    0x20000014   0x080059c0   0x00000008   Data   RW          378    .data               crc.o
    0x2000001c   0x080059c8   0x0000000c   Data   RW         1085    .data               stm32f1xx_hal.o
    0x20000028   0x080059d4   0x00000004   Data   RW         4196    .data               system_stm32f1xx.o
    0x2000002c   0x080059d8   0x00000020   Data   RW         4235    .data               motor_ctrl.o
    0x2000004c   0x080059f8   0x00000008   Data   RW         4282    .data               speed.o
    0x20000054   0x08005a00   0x00000001   Data   RW         4320    .data               transmit.o
    0x20000055   0x08005a01   0x00000001   PAD
    0x20000056   0x08005a02   0x00000002   Data   RW         4533    .data               nrf24l01.o
    0x20000058   0x08005a04   0x0000003c   Data   RW         5245    .data               tasks.o
    0x20000094   0x08005a40   0x00000014   Data   RW         5561    .data               timers.o
    0x200000a8   0x08005a54   0x00000004   Data   RW         5758    .data               cmsis_os2.o
    0x200000ac   0x08005a58   0x00000018   Data   RW         6121    .data               heap_4.o
    0x200000c4   0x08005a70   0x0000000c   Data   RW         6172    .data               port.o
    0x200000d0        -       0x00000021   Zero   RW          253    .bss                freertos.o
    0x200000f1   0x08005a7c   0x00000003   PAD
    0x200000f4        -       0x00000030   Zero   RW          336    .bss                adc.o
    0x20000124        -       0x00000054   Zero   RW          420    .bss                i2c.o
    0x20000178        -       0x00000058   Zero   RW          462    .bss                spi.o
    0x200001d0        -       0x000000d8   Zero   RW          509    .bss                tim.o
    0x200002a8        -       0x00000048   Zero   RW          576    .bss                usart.o
    0x200002f0        -       0x00000048   Zero   RW          712    .bss                stm32f1xx_hal_timebase_tim.o
    0x20000338        -       0x0000004c   Zero   RW         4234    .bss                motor_ctrl.o
    0x20000384        -       0x00000060   Zero   RW         4319    .bss                transmit.o
    0x200003e4        -       0x00000040   Zero   RW         4828    .bss                queue.o
    0x20000424        -       0x000004c4   Zero   RW         5244    .bss                tasks.o
    0x200008e8        -       0x00000118   Zero   RW         5560    .bss                timers.o
    0x20000a00        -       0x000006b8   Zero   RW         5757    .bss                cmsis_os2.o
    0x200010b8        -       0x00000c00   Zero   RW         6120    .bss                heap_4.o
    0x20001cb8        -       0x00000060   Zero   RW         6391    .bss                c_w.l(libspace.o)
    0x20001d18        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20001f18        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       168         22          0          0         48       1629   adc.o
       380         32          0          4       1720      14310   cmsis_os2.o
        76         18          0          8          0       1479   crc.o
         0          0          0          0          0      20168   event_groups.o
       296         64        196         17         33       6869   freertos.o
       212         16          0          0          0        979   gpio.o
       460         24          0         24       3072       4655   heap_4.o
       160         28          0          0         84       1617   i2c.o
       138          0          0          0          0       3382   list.o
       272         14          0          1          0     466983   main.o
      1228        106          0         32         76       4627   motor_ctrl.o
       784         80         10          2          0       7393   nrf24l01.o
       662         70          0         12          0      10761   port.o
      1512         20          0          0         64      16840   queue.o
        44         10          0          8          0       1497   speed.o
       200         26          0          0         88       1673   spi.o
        64         26        236          0       1536        800   startup_stm32f103xb.o
       100         18          0         12          0       5222   stm32f1xx_hal.o
       622         24          0          0          0       3284   stm32f1xx_hal_adc.o
       158         14          0          0          0      28174   stm32f1xx_hal_cortex.o
        34          0          0          0          0       1035   stm32f1xx_hal_crc.o
       222          4          0          0          0       1759   stm32f1xx_hal_dma.o
       490         34          0          0          0       2880   stm32f1xx_hal_gpio.o
       392         16          0          0          0       3203   stm32f1xx_hal_i2c.o
        72         10          0          0          0        866   stm32f1xx_hal_msp.o
      1304         94         18          0          0       5713   stm32f1xx_hal_rcc.o
       236         12          0          0          0       1380   stm32f1xx_hal_rcc_ex.o
       888          4          0          0          0       4733   stm32f1xx_hal_spi.o
      1760         58          0          0          0      14662   stm32f1xx_hal_tim.o
       108         12          0          0          0       2389   stm32f1xx_hal_tim_ex.o
       152         20          0          0         72       1467   stm32f1xx_hal_timebase_tim.o
      1554         10          0          0          0      10848   stm32f1xx_hal_uart.o
        36         12          0          0          0       3431   stm32f1xx_it.o
         0          0          0          0          0       8904   stream_buffer.o
         2          0         24          4          0       1107   system_stm32f1xx.o
      2064        170          0         60       1220      23148   tasks.o
       652         60          0          0        216       4251   tim.o
       912         82          0         20        280      25939   timers.o
       194         24          0          1         96       2924   transmit.o
       184         22          0          0         72       1670   usart.o

    ----------------------------------------------------------------------
     18832       <USER>        <GROUP>        208       8680     724651   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          0          3          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        16          0          0          0          0         68   aeabi_memset.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       804         16          0          0          0        272   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
        94          4          0          0          0         92   dfix.o
        46          0          0          0          0         68   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        12          4          0          0          0         68   fcheck1.o
        54          4          0          0          0         84   ffix.o
        48          0          0          0          0         68   fflt_clz.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        76          0          0          0          0         68   scalbnf.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      3608         <USER>          <GROUP>          0         96       2784   Library Totals
        16          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       672         16          0          0         96        948   c_w.l
      2920         72          0          0          0       1836   fz_ws.l

    ----------------------------------------------------------------------
      3608         <USER>          <GROUP>          0         96       2784   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     22440       1344        516        208       8776     716355   Grand Totals
     22440       1344        516        208       8776     716355   ELF Image Totals
     22440       1344        516        208          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                22956 (  22.42kB)
    Total RW  Size (RW Data + ZI Data)              8984 (   8.77kB)
    Total ROM Size (Code + RO Data + RW Data)      23164 (  22.62kB)

==============================================================================

