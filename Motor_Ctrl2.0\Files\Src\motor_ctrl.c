#include "motor_ctrl.h"
#include "tim.h"
#include "speed.h"
#include "transmit.h"

#define MAX_OUTPUT 500  // 从300提高到500

uint8_t positive[4];


int16_t X_expect;
int16_t Y_expect;
int16_t Z_expect;
int16_t height_expect;
float float_height;

remote_t remote;

float kp=30.0f;   // 从20.0提高到30.0
float ki=1.0f;    // 从2.5降低到1.0
float kd=2.0f;    // 从1.5提高到2.0
	

pid_t motor[4];

void remote_recieve()
{
	//·Ϊֵٶȵ�ת��

	// 设置通信帧头
	main_board.head = 0x51;

	X_expect=-(remote.rocker[1]-1540)/100;  // 左摇杆X (horizontal) 中值校准: 1725→1540
	Y_expect=(remote.rocker[0]-2408)/100;   // 左摇杆Y (forward) 中值校准: 1533→2408
	Z_expect=(remote.rocker[2]-1444)/100;   // 右摇杆X (C-Clockwise) 中值校准: 1443→1444
	height_expect=-(remote.rocker[3]-2524)/100; // 右摇杆Y (Height) 中值校准: 1562→2524

	float_height+=0.025*height_expect;

	if(float_height<10.0) float_height=10.1;
	else if(float_height>40) float_height=39.9;

	if(remote.status==0)
		main_board.servo_mode_and_height=0; // 完全停止
	else if(remote.status==1)
		main_board.servo_mode_and_height=100; // 轮式模式标识，使用特殊值100
	else if(remote.status==2)
		main_board.servo_mode_and_height=3; // 步行前进模式
	else if(remote.status==3)
		main_board.servo_mode_and_height=1; // 步行停止
	else if(remote.status==4)
		main_board.servo_mode_and_height=8; // 步行后退模式

	// 设置其他必要的数据字段
	main_board.X_expect = X_expect;
	main_board.Y_expect = Y_expect;
	main_board.Z_expect = Z_expect;

}

void motor_ctrl_at_TB6612()
{
	static uint8_t i;
	
	// 降低速度系数（从2.0降低到1.0）并反转Y方向（从-2*Y_expect改为2*Y_expect）
	if(OK)
	{
		// 提高Y轴系数（从1.0提高到1.5）
		motor[0].expect=(int16_t)(-0.8*X_expect+1.5*Y_expect-0.8*Z_expect);
		motor[1].expect=(int16_t)(0.8*X_expect+1.5*Y_expect-0.8*Z_expect);
		motor[2].expect=(int16_t)(0.8*X_expect-1.5*Y_expect-0.8*Z_expect);
		motor[3].expect=(int16_t)(-0.8*X_expect-1.5*Y_expect-0.8*Z_expect);
		
		// 限制期望值范围
		for(i=0; i<4; i++) {
			if(motor[i].expect > 250) motor[i].expect = 250;
			else if(motor[i].expect < -250) motor[i].expect = -250;
		}
	}
	
	// 对所有四个电机进行PID控制
	for(i=0;i<4;i++)
	{
		motor[i].current=motor_speed[i];
		PID_Cal(&motor[i]);
	}
	
	// 强制限制输出值，确保速度不会过快
	for(i=0; i<4; i++) {
		if(motor[i].output > 400) motor[i].output = 400;
		else if(motor[i].output < -400) motor[i].output = -400;
	}
	
	//·Ϊٶȿƺ
	for(i=0;i<4;++i)
	{
		if(motor[i].output>0)
			positive[i]=1;
		else
		{
			positive[i]=0;
		}
	}

	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_12,positive[0]);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_13,!positive[0]);
	
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_14,positive[1]);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_15,!positive[1]);
	
	HAL_GPIO_WritePin(GPIOA,GPIO_PIN_15,positive[2]);
	HAL_GPIO_WritePin(GPIOC,GPIO_PIN_15,!positive[2]);
	
	HAL_GPIO_WritePin(GPIOC,GPIO_PIN_14,positive[3]);
	HAL_GPIO_WritePin(GPIOC,GPIO_PIN_13,!positive[3]);	
	
	if(positive[0])
	{
		// 添加最小启动值，确保电机启动时有足够的电压
		int16_t output = motor[0].output;
		if(output > 0 && output < 100) output = 100;
		TIM3->CCR1=output > 400 ? 400 : output;
	}
	else
	{
		int16_t output = -motor[0].output;
		if(output > 0 && output < 100) output = 100;
		TIM3->CCR1=output > 400 ? 400 : output;
	}
	
	if(positive[1])
	{
		int16_t output = motor[1].output;
		if(output > 0 && output < 100) output = 100;
		TIM3->CCR2=output > 400 ? 400 : output;
	}
	else
	{
		int16_t output = -motor[1].output;
		if(output > 0 && output < 100) output = 100;
		TIM3->CCR2=output > 400 ? 400 : output;
	}
	
	if(positive[2])
	{
		int16_t output = motor[2].output;
		if(output > 0 && output < 150) output = 150; // 前轮使用更高的最小启动值
		TIM3->CCR3=output > 400 ? 400 : output;
	}
	else
	{
		int16_t output = -motor[2].output;
		if(output > 0 && output < 150) output = 150; // 前轮使用更高的最小启动值
		TIM3->CCR3=output > 400 ? 400 : output;
	}
	
	if(positive[3])
	{
		int16_t output = motor[3].output;
		if(output > 0 && output < 150) output = 150; // 前轮使用更高的最小启动值
		TIM3->CCR4=output > 400 ? 400 : output;
	}
	else
	{
		int16_t output = -motor[3].output;
		if(output > 0 && output < 150) output = 150; // 前轮使用更高的最小启动值
		TIM3->CCR4=output > 400 ? 400 : output;
	}
}

void PID_Init()
{
	uint8_t i;
	for(i=0;i<4;i++)
	{
		motor[i].Error_last=0.0f;
		motor[i].Error_prev=0.0f;
		motor[i].output=0;    // 确保输出初始化为0
		motor[i].expect=0;    // 确保期望值初始化为0
		motor[i].current=0;   // 确保当前值初始化为0
		motor[i].I=0.0f;      // 确保积分项初始化为0
	}
}

void PID_Cal(pid_t* T) //����ʽ
{
	short Error = T->expect - T->current;
	short pwm_add=0;
	
	// 增加比例项的影响，减少积分项的影响
	pwm_add = kp*(Error - T->Error_last) + ki*Error*0.5 + kd*(Error-2.0f*T->Error_last+T->Error_prev);
	
	// 更新积分项，但减少其影响
	T->I+=ki*Error*0.5;
	
	// 限制积分项大小，防止积分饱和
	if(T->I > 300) T->I = 300;
	else if(T->I < -300) T->I = -300;
	
	T->Error_prev = T->Error_last;	  	    // ϴ
    T->Error_last = Error;	              // ϴƫ
	
	// 重置输出，而不是累加
	T->output = (int16_t)pwm_add + (int16_t)T->I;
	
	// 限制输出范围
	if(T->output > MAX_OUTPUT) T->output = MAX_OUTPUT;
	else if(T->output < -MAX_OUTPUT) T->output = -MAX_OUTPUT;
}