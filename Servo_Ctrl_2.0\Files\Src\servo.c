#include <servo.h>


extern uint8_t servos[15];
uint8_t tmp[12];
uint16_t PWM_value[12];
uint16_t turn=0;

uint8_t servos_lock=0;

void Servo_data_rec()
{
	int i;
	turn=0; //��turn����λ��0

	// 添加数据有效性检查，防止舵机乱跑
	if(servos[0]==0x51&&servos[14]==0x50&&servos_lock!=1)  //���ÿ���֡
	{
		// 添加舵机角度范围检查，防止异常数据
		uint8_t data_valid = 1;
		for(i=0;i<12;i++)
		{
			if(servos[i+1] > 180) // 舵机角度不应超过180度
			{
				data_valid = 0;
				printf("Invalid servo data: servo %u = %u\r\n", i+1, servos[i+1]);
				break;
			}
		}

		if(data_valid)
		{
			for(i=0;i<12;i++)
			{
				if(servos[i+1]!=tmp[i])
				{
					tmp[i]=servos[i+1];
					turn|=(1<<i);
				}
			}
	 //����׼ȷ������£���������

			for(i=0;i<12;i++) //��ӡ���ĵĶ��ֵ
			{
				if(turn&(1<<i))
					printf("servo %u turn to %u\r\n",i+1,tmp[i]);
			}
		}
		else
		{
			printf("Invalid servo data frame, ignoring...\r\n");
		}
	}
	else if(servos[0]==0x53&&servos[1]==0x52) //��������֡
	{
		servos_lock=1;
		printf("servos lock!\r\n");
	}
	else if(servos[0]==0x55&&servos[1]==0x54) //����֡
	{
		servos_lock=0;
		printf("servos unlock!\r\n");
	}
	else
	{
		printf("data error: head=0x%02X, tail=0x%02X\r\n", servos[0], servos[14]);
	}
}


void Servo_calc()
{
	uint8_t i;

	// 如果舵机被锁定，不执行任何动作
	if(servos_lock == 1)
	{
		printf("Servos are locked, no movement allowed\r\n");
		return;
	}

	for(i=0;i<12;i++)
	{
		if(turn&(1<<i)&&tmp[i]<=180) // 确保角度在有效范围内
		{
			PWM_value[i]=(uint16_t)(500+11.1*tmp[i]);
			// 添加PWM值范围检查
			if(PWM_value[i] < 500) PWM_value[i] = 500;   // 最小值
			if(PWM_value[i] > 2500) PWM_value[i] = 2500; // 最大值
		}
	}

	// 只有在数据有效时才更新PWM输出
	if(turn > 0)
	{
		TIM2->CCR1=PWM_value[0];
		TIM2->CCR2=PWM_value[1];
		TIM2->CCR3=PWM_value[2];
		TIM2->CCR4=PWM_value[3];
		TIM3->CCR1=PWM_value[4];
		TIM3->CCR2=PWM_value[5];
		TIM3->CCR3=PWM_value[6];
		TIM3->CCR4=PWM_value[7];
		TIM4->CCR1=PWM_value[8];
		TIM4->CCR2=PWM_value[9];
		TIM4->CCR3=PWM_value[10];
		TIM4->CCR4=PWM_value[11];
	}
}


void Servo_init()
{
	static uint8_t i;

	// 初始化时锁定舵机，防止上电时乱跑
	servos_lock = 1;

	TIM2->CCR1=1500;
	TIM2->CCR2=1500;
	TIM2->CCR3=1500;
	TIM2->CCR4=1500;
	TIM3->CCR1=1500;
	TIM3->CCR2=1500;
	TIM3->CCR3=1500;
	TIM3->CCR4=1500;
	TIM4->CCR1=1500;
	TIM4->CCR2=1500;
	TIM4->CCR3=1500;
	TIM4->CCR4=1500;

	for(i=0;i<12;i++)
	{
		PWM_value[i]=1500;
		tmp[i]=90;
	}

	printf("Servo initialized, servos locked for safety\r\n");
  // Servo_calc(); // 初始化时不调用，避免乱跑
}

// 紧急停止函数
void Servo_emergency_stop()
{
	servos_lock = 1;

	// 将所有舵机设置为中位
	TIM2->CCR1=1500;
	TIM2->CCR2=1500;
	TIM2->CCR3=1500;
	TIM2->CCR4=1500;
	TIM3->CCR1=1500;
	TIM3->CCR2=1500;
	TIM3->CCR3=1500;
	TIM3->CCR4=1500;
	TIM4->CCR1=1500;
	TIM4->CCR2=1500;
	TIM4->CCR3=1500;
	TIM4->CCR4=1500;

	printf("EMERGENCY STOP: All servos locked and set to center position\r\n");
}