Dependencies for Project 'Motor_Ctrl2.0_Auxiliary', Target 'Motor_Ctrl2.0_Auxiliary': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (startup_stm32f103xb.s)(0x686EC081)(--cpu Cortex-M3 -g --apcs=interwork -I ../Core/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

--pd "__UVISION_VERSION SETA 532" --pd "_RTE_ SETA 1" --pd "STM32F10X_MD SETA 1" --pd "_RTE_ SETA 1"

--list startup_stm32f103xb.lst --xref -o motor_ctrl2.0_auxiliary\startup_stm32f103xb.o --depend motor_ctrl2.0_auxiliary\startup_stm32f103xb.d)
F (../Core/Src/main.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\main.o --omf_browse motor_ctrl2.0_auxiliary\main.crf --depend motor_ctrl2.0_auxiliary\main.d)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Core/Inc/crc.h)(0x686EC07A)
I (../Core/Inc/i2c.h)(0x686EC07A)
I (../Core/Inc/spi.h)(0x686EC07A)
I (../Core/Inc/tim.h)(0x686EC07A)
I (../Core/Inc/usart.h)(0x686EC07A)
I (../Core/Inc/gpio.h)(0x686EC07A)
I (../Files/Inc/speed.h)(0x686EC081)
F (../Core/Src/gpio.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\gpio.o --omf_browse motor_ctrl2.0_auxiliary\gpio.crf --depend motor_ctrl2.0_auxiliary\gpio.d)
I (../Core/Inc/gpio.h)(0x686EC07A)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/freertos.c)(0x687D1A3B)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\freertos.o --omf_browse motor_ctrl2.0_auxiliary\freertos.crf --depend motor_ctrl2.0_auxiliary\freertos.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x686EC082)
I (../Files/Inc/transmit.h)(0x686EC081)
I (../Files/Inc/servo_ctrl.h)(0x686EC081)
I (../Core/Inc/usart.h)(0x686EC07A)
I (../Files/Inc/walk.h)(0x686EC081)
I (../Files/Inc/speed.h)(0x686EC081)
I (../Core/Inc/tim.h)(0x686EC07A)
F (../Core/Src/crc.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\crc.o --omf_browse motor_ctrl2.0_auxiliary\crc.crf --depend motor_ctrl2.0_auxiliary\crc.d)
I (../Core/Inc/crc.h)(0x686EC07A)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/i2c.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\i2c.o --omf_browse motor_ctrl2.0_auxiliary\i2c.crf --depend motor_ctrl2.0_auxiliary\i2c.d)
I (../Core/Inc/i2c.h)(0x686EC07A)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/spi.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\spi.o --omf_browse motor_ctrl2.0_auxiliary\spi.crf --depend motor_ctrl2.0_auxiliary\spi.d)
I (../Core/Inc/spi.h)(0x686EC07A)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/tim.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\tim.o --omf_browse motor_ctrl2.0_auxiliary\tim.crf --depend motor_ctrl2.0_auxiliary\tim.d)
I (../Core/Inc/tim.h)(0x686EC07A)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/usart.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\usart.o --omf_browse motor_ctrl2.0_auxiliary\usart.crf --depend motor_ctrl2.0_auxiliary\usart.d)
I (../Core/Inc/usart.h)(0x686EC07A)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/stm32f1xx_it.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_it.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_it.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_it.d)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_it.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
F (../Core/Src/stm32f1xx_hal_msp.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_msp.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_msp.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_msp.d)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_gpio_ex.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_gpio_ex.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_gpio_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_crc.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_crc.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_crc.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_crc.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_rcc.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_rcc.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_rcc.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_rcc_ex.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_rcc_ex.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_rcc_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_gpio.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_gpio.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_gpio.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_dma.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_dma.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_dma.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_cortex.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_cortex.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_cortex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_pwr.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_pwr.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_pwr.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_flash.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_flash.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_flash.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_flash_ex.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_flash_ex.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_flash_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_exti.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_exti.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_exti.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_i2c.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_i2c.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_i2c.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_spi.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_spi.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_spi.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_tim.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_tim.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_tim.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_tim_ex.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_tim_ex.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_tim_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c)(0x686EC080)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stm32f1xx_hal_uart.o --omf_browse motor_ctrl2.0_auxiliary\stm32f1xx_hal_uart.crf --depend motor_ctrl2.0_auxiliary\stm32f1xx_hal_uart.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Core/Src/system_stm32f1xx.c)(0x686EC07A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\system_stm32f1xx.o --omf_browse motor_ctrl2.0_auxiliary\system_stm32f1xx.crf --depend motor_ctrl2.0_auxiliary\system_stm32f1xx.d)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (../Middlewares/Third_Party/FreeRTOS/Source/croutine.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\croutine.o --omf_browse motor_ctrl2.0_auxiliary\croutine.crf --depend motor_ctrl2.0_auxiliary\croutine.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/croutine.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\event_groups.o --omf_browse motor_ctrl2.0_auxiliary\event_groups.crf --depend motor_ctrl2.0_auxiliary\event_groups.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdlib.h)(0x5F63877C)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/list.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\list.o --omf_browse motor_ctrl2.0_auxiliary\list.crf --depend motor_ctrl2.0_auxiliary\list.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdlib.h)(0x5F63877C)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/queue.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\queue.o --omf_browse motor_ctrl2.0_auxiliary\queue.crf --depend motor_ctrl2.0_auxiliary\queue.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdlib.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\string.h)(0x5F63878A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\stream_buffer.o --omf_browse motor_ctrl2.0_auxiliary\stream_buffer.crf --depend motor_ctrl2.0_auxiliary\stream_buffer.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\string.h)(0x5F63878A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/stream_buffer.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/tasks.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\tasks.o --omf_browse motor_ctrl2.0_auxiliary\tasks.crf --depend motor_ctrl2.0_auxiliary\tasks.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdlib.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\string.h)(0x5F63878A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/stack_macros.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/timers.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\timers.o --omf_browse motor_ctrl2.0_auxiliary\timers.crf --depend motor_ctrl2.0_auxiliary\timers.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdlib.h)(0x5F63877C)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\cmsis_os2.o --omf_browse motor_ctrl2.0_auxiliary\cmsis_os2.crf --depend motor_ctrl2.0_auxiliary\cmsis_os2.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\string.h)(0x5F63878A)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\heap_4.o --omf_browse motor_ctrl2.0_auxiliary\heap_4.crf --depend motor_ctrl2.0_auxiliary\heap_4.d)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdlib.h)(0x5F63877C)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/port.c)(0x686EC082)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\port.o --omf_browse motor_ctrl2.0_auxiliary\port.crf --depend motor_ctrl2.0_auxiliary\port.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x686EC082)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Core/Inc/FreeRTOSConfig.h)(0x686EC07A)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/portmacro.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x686EC082)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x686EC082)
F (..\Files\Src\speed.c)(0x686EC081)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\speed.o --omf_browse motor_ctrl2.0_auxiliary\speed.crf --depend motor_ctrl2.0_auxiliary\speed.d)
I (../Files/Inc/speed.h)(0x686EC081)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
I (../Core/Inc/tim.h)(0x686EC07A)
F (..\Files\Src\transmit.c)(0x687D1A2D)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\transmit.o --omf_browse motor_ctrl2.0_auxiliary\transmit.crf --depend motor_ctrl2.0_auxiliary\transmit.d)
I (../Files/Inc/transmit.h)(0x686EC081)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
I (../Core/Inc/usart.h)(0x686EC07A)
I (D:\Keil5\Keil5\ARM\ARMCC\include\string.h)(0x5F63878A)
I (../Files/Inc/servo_ctrl.h)(0x686EC081)
I (../Core/Inc/crc.h)(0x686EC07A)
F (..\Files\Src\servo_ctrl.c)(0x686EC081)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\servo_ctrl.o --omf_browse motor_ctrl2.0_auxiliary\servo_ctrl.crf --depend motor_ctrl2.0_auxiliary\servo_ctrl.d)
I (../Files/Inc/servo_ctrl.h)(0x686EC081)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
F (..\Files\Src\walk.c)(0x687D1A1A)(--c99 -c --cpu Cortex-M3 -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3 -I ../Files/Inc

-I.\RTE\_Motor_Ctrl2.0_Auxiliary

-ID:\Keil5\keil5pack\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\keil5pack\Keil\STM32F1xx_DFP\2.4.0\Device\Include

-D__UVISION_VERSION="532" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o motor_ctrl2.0_auxiliary\walk.o --omf_browse motor_ctrl2.0_auxiliary\walk.crf --depend motor_ctrl2.0_auxiliary\walk.d)
I (../Files/Inc/walk.h)(0x686EC081)
I (../Core/Inc/main.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x686EC080)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x686EC07A)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x686EC080)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x686EC07E)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x686EC07E)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x686EC07F)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stdint.h)(0x5F63877C)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x686EC07F)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x686EC07F)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x686EC07E)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x686EC080)
I (D:\Keil5\Keil5\ARM\ARMCC\include\stddef.h)(0x5F63877C)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_crc.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_i2c.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_spi.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_tim_ex.h)(0x686EC080)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x686EC080)
I (../Files/Inc/transmit.h)(0x686EC081)
I (D:\Keil5\Keil5\ARM\ARMCC\include\math.h)(0x5F63877C)
I (../Files/Inc/servo_ctrl.h)(0x686EC081)
